<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>SVGExport</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./SVGExport.html" class="internal-link">SVGExport</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>SVGExport class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;"></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">SVGExport</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div id="fragment-1723"></div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./SVGExport.BmpExportType.html" class="internal-link">BmpExportType</a></td>
          <td>BMP导出类型</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./SVGExport.CharacterSubsetting.html" class="internal-link">CharacterSubsetting</a></td>
          <td>字符子集</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./SVGExport.DrawingPrecision.html" class="internal-link">DrawingPrecision</a></td>
          <td>绘图精度</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./SVGExport.EmbedFont.html" class="internal-link">EmbedFont</a></td>
          <td>嵌入字体</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./SVGExport.EmbedImages.html" class="internal-link">EmbedImages</a></td>
          <td>嵌入图像</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./SVGExport.EmbedJavaScript.html" class="internal-link">EmbedJavaScript</a></td>
          <td>嵌入JavaScript</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./SVGExport.Encoding.html" class="internal-link">Encoding</a></td>
          <td>编码</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./SVGExport.FountainSteps.html" class="internal-link">FountainSteps</a></td>
          <td>渐变台阶</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./SVGExport.GiveEmbededFontPriority.html" class="internal-link">GiveEmbededFontPriority</a></td>
          <td>给嵌入字体优先级</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./SVGExport.HasDialog.html" class="internal-link">HasDialog</a></td>
          <td>有对话框</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./SVGExport.Height.html" class="internal-link">Height</a></td>
          <td>高度</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./SVGExport.Styling.html" class="internal-link">Styling</a></td>
          <td>造型</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./SVGExport.TextAsCurves.html" class="internal-link">TextAsCurves</a></td>
          <td>文本曲线</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./SVGExport.Units.html" class="internal-link">Units</a></td>
          <td>单位</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./SVGExport.Version.html" class="internal-link">Version</a></td>
          <td>版本</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./SVGExport.Width.html" class="internal-link">Width</a></td>
          <td>宽度</td>
          <td></td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1726" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,15">
<div class="content-fragment-content">


      <div class="content-fragment-header">方法:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./SVGExport.Finish.html" class="internal-link">Finish</a></td>
          <td>结束</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./SVGExport.Reset.html" class="internal-link">Reset</a></td>
          <td>重启</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./SVGExport.ShowDialog.html" class="internal-link">ShowDialog</a></td>
          <td>显示对话框</td>
          <td></td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>SubPath.GetIntersections</title> 
                        </head><body>
                        <div><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><a href="./SubPath.html" class="internal-link"><strong>SubPath</strong></a></div>
<div class="content-fragment-header"><a href="./SubPath.GetIntersections.html" class="internal-link"><strong>SubPath.GetIntersections</strong></a></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">找到与另一个子路径的交点</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <div class="content-fragment-header">语法:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
      <div class="code-block"><span class="keyword">Function</span><span class="space"> </span> <span class="identifier">GetIntersections</span><span class="punctuation">(</span><span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">Target</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./SubPath.html" class="internal-link">SubPath</a></span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">OffsetType</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="https://community.coreldraw.com/sdk/api/draw/17/e/cdrSegmentOffsetType.html" class="internal-link">cdrSegmentOffsetType</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="namespace">cdrRelativeSegmentOffset</span><span class="punctuation">)</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./CrossPoints.html" class="internal-link">CrossPoints</a></span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1721" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,10">
<div class="content-fragment-content">


      <div class="content-fragment-header">参数:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名称</th>
          <th>类型</th>
          <th>描述</th>
        </tr>
            <tr>
          <td>Target</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./SubPath.html" class="internal-link">SubPath</a></span></div>
                      </td>
          <td><div>指定与之相交的子路径</div></td>
        </tr>
            <tr>
          <td>OffsetType</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./cdrSegmentOffsetType.html" class="internal-link">cdrSegmentOffsetType</a></span></div>
                      </td>
          <td><div>指定交点的偏移类型,并返回 <a href="./cdrSegmentOffsetType.html">cdrSegmentOffsetType</a>。</div></td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>GetIntersections</b> 方法查找两个子路径的所有交点。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div id="fragment-1725"></div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1730" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,19">
<div class="content-fragment-content">


      <div class="content-fragment-header">示例:</div>
            <div style="margin-bottom: 1em">下面的 VBA 示例显示两条选定曲线相交的次数,并用一个小圆圈标记每个相交点。</div>
        <div><pre style="code">Sub Test()<br>
 Dim sr As ShapeRange<br>
 Dim sp1 As SubPath, sp2 As SubPath<br>
 Dim cps As CrossPoints, cp As CrossPoint<br>
 Dim x As Double, y As Double, n As Long<br>
 Set sr = ActiveSelectionRange<br>
 If sr.Count &lt;&gt; 2 Then<br>
 MsgBox "Please select two curves", vbCritical<br>
 Exit Sub<br>
 End If<br>
 If sr(1).Type &lt;&gt; cdrCurveShape Or sr(2).Type &lt;&gt; cdrCurveShape Then<br>
 MsgBox "One of the selected shapes is not a curve", vbCritical<br>
 Exit Sub<br>
 End If<br>
 n = 0<br>
 For Each sp1 In sr(1).Curve.Subpaths<br>
 For Each sp2 In sr(2).Curve.Subpaths<br>
 Set cps = sp1.GetIntersections(sp2)<br>
 For Each cp In cps<br>
 ActiveLayer.CreateEllipse2 cp.PositionX, cp.PositionY, 0.05<br>
 Next cp<br>
 n = n + cps.Count<br>
 Next sp2<br>
 Next sp1<br>
 MsgBox n &amp; " intersection point(s) found"<br>
End Sub</pre></div>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
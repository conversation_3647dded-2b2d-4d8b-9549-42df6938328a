<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>StructColorConversionOptions</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./StructColorConversionOptions.html" class="internal-link">StructColorConversionOptions</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>StructColorConversionOptions class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">StructColorConversionOptions 类</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">StructColorConversionOptions</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>StructColorConversionOptions</b> 类表示颜色转换设置。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./StructColorConversionOptions.ColorConversionHandler.html" class="internal-link">ColorConversionHandler</a></td>
          <td>颜色转换处理程序</td>
          <td>指定在打开/导入/粘贴图形时遇到缺少或不匹配的配置文件时要使用的自定义回调接口</td>
        </tr>
            <tr>
          <td><a href="./StructColorConversionOptions.ColorPolicy.html" class="internal-link">ColorPolicy</a></td>
          <td>颜色政策</td>
          <td>指定打开/导入/粘贴图形时要使用的默认颜色管理策略</td>
        </tr>
            <tr>
          <td><a href="./StructColorConversionOptions.SourceColorProfileList.html" class="internal-link">SourceColorProfileList</a></td>
          <td>源颜色配置文件列表</td>
          <td>指定要用作源颜色上下文的配置文件名称列表,用于转换正在打开/导入/粘贴的对象的颜色信息</td>
        </tr>
            <tr>
          <td><a href="./StructColorConversionOptions.TargetColorProfileList.html" class="internal-link">TargetColorProfileList</a></td>
          <td>目标颜色配置文件列表</td>
          <td>指定要在导入/打开/粘贴对象后将文档颜色转换为的配置文件名称列表。这会改变整个文档的颜色上下文</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
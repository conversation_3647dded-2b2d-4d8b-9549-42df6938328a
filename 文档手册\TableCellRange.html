<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>TableCellRange</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./TableCellRange.html" class="internal-link">TableCellRange</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>TableCellRange class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;"></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">TableCellRange</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>TableCellRange</b> 类表示表格单元格数组(或范围)的设置。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./TableCellRange._NewEnum.html" class="internal-link">_NewEnum</a></td>
          <td>_New枚举</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./TableCellRange.Borders.html" class="internal-link">Borders</a></td>
          <td>边框</td>
          <td>返回范围内单元格的边框属性</td>
        </tr>
            <tr>
          <td><a href="./TableCellRange.CanMerge.html" class="internal-link">CanMerge</a></td>
          <td>可以合并</td>
          <td>确定范围内的单元格是否可以合并</td>
        </tr>
            <tr>
          <td><a href="./TableCellRange.Columns.html" class="internal-link">Columns</a></td>
          <td>列</td>
          <td>返回范围内单元格的列集合</td>
        </tr>
            <tr>
          <td><a href="./TableCellRange.Count.html" class="internal-link">Count</a></td>
          <td>数数</td>
          <td>返回范围内的单元格数</td>
        </tr>
            <tr>
          <td><a href="./TableCellRange.IsRectangular.html" class="internal-link">IsRectangular</a></td>
          <td>是矩形</td>
          <td>确定范围内的单元格形成一个矩形区域</td>
        </tr>
            <tr>
          <td><a href="./TableCellRange.Item.html" class="internal-link">Item</a></td>
          <td>物品</td>
          <td>从范围内返回指定的单元格</td>
        </tr>
            <tr>
          <td><a href="./TableCellRange.Rows.html" class="internal-link">Rows</a></td>
          <td>行</td>
          <td>返回范围内单元格的行集合</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1726" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,15">
<div class="content-fragment-content">


      <div class="content-fragment-header">方法:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./TableCellRange.AddCell.html" class="internal-link">AddCell</a></td>
          <td>添加单元格</td>
          <td>将单元格添加到范围</td>
        </tr>
            <tr>
          <td><a href="./TableCellRange.AddColumn.html" class="internal-link">AddColumn</a></td>
          <td>添加列</td>
          <td>将列的所有单元格添加到范围</td>
        </tr>
            <tr>
          <td><a href="./TableCellRange.AddRange.html" class="internal-link">AddRange</a></td>
          <td>添加范围</td>
          <td>将另一个范围的所有单元格添加到当前范围</td>
        </tr>
            <tr>
          <td><a href="./TableCellRange.AddRow.html" class="internal-link">AddRow</a></td>
          <td>添加行</td>
          <td>将一行的所有单元格添加到范围</td>
        </tr>
            <tr>
          <td><a href="./TableCellRange.AddToSelection.html" class="internal-link">AddToSelection</a></td>
          <td>添加到选择</td>
          <td>将范围内的所有单元格添加到当前选择</td>
        </tr>
            <tr>
          <td><a href="./TableCellRange.ApplyFill.html" class="internal-link">ApplyFill</a></td>
          <td>应用填充</td>
          <td>对范围内的所有单元格应用填充</td>
        </tr>
            <tr>
          <td><a href="./TableCellRange.Merge.html" class="internal-link">Merge</a></td>
          <td>合并</td>
          <td>合并范围内的单元格</td>
        </tr>
            <tr>
          <td><a href="./TableCellRange.Remove.html" class="internal-link">Remove</a></td>
          <td>消除</td>
          <td>按单元格索引从范围中删除一个单元格</td>
        </tr>
            <tr>
          <td><a href="./TableCellRange.RemoveCell.html" class="internal-link">RemoveCell</a></td>
          <td>删除单元格</td>
          <td>从范围中删除一个单元格</td>
        </tr>
            <tr>
          <td><a href="./TableCellRange.RemoveRange.html" class="internal-link">RemoveRange</a></td>
          <td>删除范围</td>
          <td>从当前范围中删除一系列单元格</td>
        </tr>
            <tr>
          <td><a href="./TableCellRange.Select.html" class="internal-link">Select</a></td>
          <td>选择</td>
          <td>选择范围内的所有单元格</td>
        </tr>
            <tr>
          <td><a href="./TableCellRange.Unmerge.html" class="internal-link">Unmerge</a></td>
          <td>取消合并</td>
          <td>取消合并范围内的所有合并单元格</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
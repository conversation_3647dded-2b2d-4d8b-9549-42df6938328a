<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>StyleOutline</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./StyleOutline.html" class="internal-link">StyleOutline</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>StyleOutline class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">StyleOutline 类</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">StyleOutline</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>StyleOutline </b> 类表示轮廓样式的设置。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./StyleOutline.BehindFill.html" class="internal-link">BehindFill</a></td>
          <td>后面填充</td>
          <td>指定是否应在填充后面绘制轮廓</td>
        </tr>
            <tr>
          <td><a href="./StyleOutline.Color.html" class="internal-link">Color</a></td>
          <td>颜色</td>
          <td>指定轮廓的颜色</td>
        </tr>
            <tr>
          <td><a href="./StyleOutline.Justification.html" class="internal-link">Justification</a></td>
          <td>理由</td>
          <td>指定轮廓对齐</td>
        </tr>
            <tr>
          <td><a href="./StyleOutline.LineCaps.html" class="internal-link">LineCaps</a></td>
          <td>线帽</td>
          <td>指定线端盖的样式</td>
        </tr>
            <tr>
          <td><a href="./StyleOutline.LineJoin.html" class="internal-link">LineJoin</a></td>
          <td>连线</td>
          <td>指定线连接样式</td>
        </tr>
            <tr>
          <td><a href="./StyleOutline.MiterLimit.html" class="internal-link">MiterLimit</a></td>
          <td>斜接限制</td>
          <td>指定斜接限制</td>
        </tr>
            <tr>
          <td><a href="./StyleOutline.NibAngle.html" class="internal-link">NibAngle</a></td>
          <td>笔尖角度</td>
          <td>指定笔尖旋转角度</td>
        </tr>
            <tr>
          <td><a href="./StyleOutline.NibStretch.html" class="internal-link">NibStretch</a></td>
          <td>笔尖拉伸</td>
          <td>指定笔尖拉伸比</td>
        </tr>
            <tr>
          <td><a href="./StyleOutline.OverlapArrow.html" class="internal-link">OverlapArrow</a></td>
          <td>重叠箭头</td>
          <td>指定箭头是否与曲线段的末端重叠</td>
        </tr>
            <tr>
          <td><a href="./StyleOutline.Overprint.html" class="internal-link">Overprint</a></td>
          <td>套印</td>
          <td>指定是否应叠印轮廓颜色</td>
        </tr>
            <tr>
          <td><a href="./StyleOutline.ScaleWithShape.html" class="internal-link">ScaleWithShape</a></td>
          <td>随形状缩放</td>
          <td>指定轮廓属性是否应随形状自动缩放</td>
        </tr>
            <tr>
          <td><a href="./StyleOutline.ShareArrow.html" class="internal-link">ShareArrow</a></td>
          <td>分享箭头</td>
          <td>指定两个箭头是否共享它们的属性</td>
        </tr>
            <tr>
          <td><a href="./StyleOutline.Style.html" class="internal-link">Style</a></td>
          <td>风格</td>
          <td>返回轮廓样式的通用样式对象</td>
        </tr>
            <tr>
          <td><a href="./StyleOutline.Type.html" class="internal-link">Type</a></td>
          <td>类型</td>
          <td>指定样式中定义的轮廓类型</td>
        </tr>
            <tr>
          <td><a href="./StyleOutline.WidelineWidth.html" class="internal-link">WidelineWidth</a></td>
          <td>宽线宽度</td>
          <td>指定宽线轮廓的宽度</td>
        </tr>
            <tr>
          <td><a href="./StyleOutline.Width.html" class="internal-link">Width</a></td>
          <td>宽度</td>
          <td>指定轮廓的宽度</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
Option Explicit

Public Sub SetSuperscriptForRange(ByVal targetRange As TextRange)
    If targetRange Is Nothing Then
        Debug.Print "SetSuperscriptForRange: targetRange is Nothing."
        Exit Sub
    End If
    
    Dim fontProps As FontProperties
    On Error Resume Next
    Set fontProps = targetRange.FontProperties
    If Err.Number <> 0 Then
        Debug.Print "SetSuperscriptForRange: Error getting FontProperties: " & Err.Description
        Err.Clear
        Exit Sub
    End If
    
    If fontProps Is Nothing Then
        Debug.Print "SetSuperscriptForRange: targetRange.FontProperties returned Nothing."
        Exit Sub
    End If
    
    Debug.Print "SetSuperscriptForRange: Attempting to set Superscript. FontProps Type: " & TypeName(fontProps)
    fontProps.Position = cdrSuperscriptFontPosition
    If Err.Number <> 0 Then
        Debug.Print "SetSuperscriptForRange: Error setting Position to Superscript: " & Err.Description
        Err.Clear
    Else
        Debug.Print "SetSuperscriptForRange: Superscript set successfully."
    End If
    On Error GoTo 0
    Set fontProps = Nothing
End Sub

Public Sub SetSubscriptForRange(ByVal targetRange As TextRange)
    If targetRange Is Nothing Then
        Debug.Print "SetSubscriptForRange: targetRange is Nothing."
        Exit Sub
    End If
    
    Dim fontProps As FontProperties
    On Error Resume Next
    Set fontProps = targetRange.FontProperties
    If Err.Number <> 0 Then
        Debug.Print "SetSubscriptForRange: Error getting FontProperties: " & Err.Description
        Err.Clear
        Exit Sub
    End If
    
    If fontProps Is Nothing Then
        Debug.Print "SetSubscriptForRange: targetRange.FontProperties returned Nothing."
        Exit Sub
    End If
    
    Debug.Print "SetSubscriptForRange: Attempting to set Subscript. FontProps Type: " & TypeName(fontProps)
    fontProps.Position = cdrSubscriptFontPosition
    If Err.Number <> 0 Then
        Debug.Print "SetSubscriptForRange: Error setting Position to Subscript: " & Err.Description
        Err.Clear
    Else
        Debug.Print "SetSubscriptForRange: Subscript set successfully."
    End If
    On Error GoTo 0
    Set fontProps = Nothing
End Sub

Public Sub CancelSubSuperscriptForRange(ByVal targetRange As TextRange)
    If targetRange Is Nothing Then
        Debug.Print "CancelSubSuperscriptForRange: targetRange is Nothing."
        Exit Sub
    End If
    
    Dim fontProps As FontProperties
    On Error Resume Next
    Set fontProps = targetRange.FontProperties
    If Err.Number <> 0 Then
        Debug.Print "CancelSubSuperscriptForRange: Error getting FontProperties: " & Err.Description
        Err.Clear
        Exit Sub
    End If
    
    If fontProps Is Nothing Then
        Debug.Print "CancelSubSuperscriptForRange: targetRange.FontProperties returned Nothing."
        Exit Sub
    End If
    
    Debug.Print "CancelSubSuperscriptForRange: Attempting to set Normal position. FontProps Type: " & TypeName(fontProps)
    fontProps.Position = cdrNormalFontPosition
    If Err.Number <> 0 Then
        Debug.Print "CancelSubSuperscriptForRange: Error setting Position to Normal: " & Err.Description
        Err.Clear
    Else
        Debug.Print "CancelSubSuperscriptForRange: Normal position set successfully."
    End If
    On Error GoTo 0
    Set fontProps = Nothing
End Sub
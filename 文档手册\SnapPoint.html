<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>SnapPoint</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./SnapPoint.html" class="internal-link">SnapPoint</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>SnapPoint class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">快照点类</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">SnapPoint</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>SnapPoint</b> 类表示形状上的捕捉点的设置。</div>
                <div>基本形状有多个对齐点 - 例如,矩形在每条边的中间都有对齐点。可以使用 <a href="./SnapPoints.AddUserSnapPoint.html">SnapPoints.AddUserSnapPoint 将其他捕捉点添加到形状</a> 方法或 <a href="./SnapPoints.AddUserSnapPointEx.html">SnapPoints.AddUserSnapPointEx</a> 方法.</div>
                <div>您可以通过在 Visual Basic 中使用 <b>New</b> 关键字来创建 <b>SnapPoint</b> 对象。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./SnapPoint.Application.html" class="internal-link">Application</a></td>
          <td>应用</td>
          <td>获取 Snap Point 所属的应用程序</td>
        </tr>
            <tr>
          <td><a href="./SnapPoint.BBox.html" class="internal-link">BBox</a></td>
          <td>盒子</td>
          <td>返回特定于边界框捕捉点的属性</td>
        </tr>
            <tr>
          <td><a href="./SnapPoint.CanChangeDirection.html" class="internal-link">CanChangeDirection</a></td>
          <td>可以改变方向</td>
          <td>如果捕捉点可以改变方向,则返回 true</td>
        </tr>
            <tr>
          <td><a href="./SnapPoint.Direction.html" class="internal-link">Direction</a></td>
          <td>方向</td>
          <td>指定捕捉点的默认方向</td>
        </tr>
            <tr>
          <td><a href="./SnapPoint.Edge.html" class="internal-link">Edge</a></td>
          <td>边缘</td>
          <td>返回特定于边缘捕捉点的属性</td>
        </tr>
            <tr>
          <td><a href="./SnapPoint.IsDeletable.html" class="internal-link">IsDeletable</a></td>
          <td>可删除</td>
          <td>如果可以删除捕捉点,则返回 true</td>
        </tr>
            <tr>
          <td><a href="./SnapPoint.IsMovable.html" class="internal-link">IsMovable</a></td>
          <td>是可移动的</td>
          <td>如果捕捉点可以移动,则返回 true</td>
        </tr>
            <tr>
          <td><a href="./SnapPoint.IsSelectable.html" class="internal-link">IsSelectable</a></td>
          <td>是可选的</td>
          <td>如果可以选择捕捉点,则返回 true</td>
        </tr>
            <tr>
          <td><a href="./SnapPoint.Object.html" class="internal-link">Object</a></td>
          <td>目的</td>
          <td>返回特定于对象捕捉点的属性</td>
        </tr>
            <tr>
          <td><a href="./SnapPoint.Parent.html" class="internal-link">Parent</a></td>
          <td>家长</td>
          <td>获取形状父级</td>
        </tr>
            <tr>
          <td><a href="./SnapPoint.PositionX.html" class="internal-link">PositionX</a></td>
          <td>位置X</td>
          <td>获取或设置 SnapPoint 的 X 坐标</td>
        </tr>
            <tr>
          <td><a href="./SnapPoint.PositionY.html" class="internal-link">PositionY</a></td>
          <td>位置Y</td>
          <td>获取或设置 SnapPoint 的 Y 坐标</td>
        </tr>
            <tr>
          <td><a href="./SnapPoint.ReferenceData.html" class="internal-link">ReferenceData</a></td>
          <td>参考数据</td>
          <td>返回捕捉点的唯一标识符</td>
        </tr>
            <tr>
          <td><a href="./SnapPoint.Selected.html" class="internal-link">Selected</a></td>
          <td>已选中</td>
          <td>指定捕捉点是否由用户选择</td>
        </tr>
            <tr>
          <td><a href="./SnapPoint.Shape.html" class="internal-link">Shape</a></td>
          <td>形状</td>
          <td>返回捕捉点所属的形状</td>
        </tr>
            <tr>
          <td><a href="./SnapPoint.Type.html" class="internal-link">Type</a></td>
          <td>类型</td>
          <td>获取 SnapPoint 类型</td>
        </tr>
            <tr>
          <td><a href="./SnapPoint.User.html" class="internal-link">User</a></td>
          <td>用户</td>
          <td>返回特定于用户捕捉点的属性</td>
        </tr>
            <tr>
          <td><a href="./SnapPoint.UsesDirection.html" class="internal-link">UsesDirection</a></td>
          <td>用途方向</td>
          <td>指定捕捉点是否使用方向</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1726" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,15">
<div class="content-fragment-content">


      <div class="content-fragment-header">方法:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./SnapPoint.CreateSelection.html" class="internal-link">CreateSelection</a></td>
          <td>创建选择</td>
          <td>选择捕捉点(同时取消选择所有其他点)</td>
        </tr>
            <tr>
          <td><a href="./SnapPoint.Delete.html" class="internal-link">Delete</a></td>
          <td>删除</td>
          <td>删除捕捉点</td>
        </tr>
            <tr>
          <td><a href="./SnapPoint.GetPosition.html" class="internal-link">GetPosition</a></td>
          <td>获取位置</td>
          <td>获取捕捉点的位置</td>
        </tr>
            <tr>
          <td><a href="./SnapPoint.Move.html" class="internal-link">Move</a></td>
          <td>移动</td>
          <td>移动捕捉点</td>
        </tr>
            <tr>
          <td><a href="./SnapPoint.SetPosition.html" class="internal-link">SetPosition</a></td>
          <td>设置位置</td>
          <td>将捕捉点移动到指定位置</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
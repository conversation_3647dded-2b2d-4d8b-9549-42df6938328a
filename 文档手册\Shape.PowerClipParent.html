<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>Shape.PowerClipParent</title> 
                        </head><body>
                        <div><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><a href="./Shape.html" class="internal-link"><strong>Shape</strong></a></div>
<div class="content-fragment-header"><a href="./Shape.PowerClipParent.html" class="internal-link"><strong>Shape.PowerClipParent</strong></a></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">返回 PowerClip 父级</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <div class="content-fragment-header">语法:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
      <div class="code-block"><span class="keyword">Property</span><span class="space"> </span> <span class="keyword">Get</span><span class="space"> </span> <span class="identifier">PowerClipParent</span><span class="punctuation">(</span><span class="punctuation">)</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./Shape.html" class="internal-link">Shape</a></span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>PowerClipParent</b> 属性返回一个表示给定形状的 PowerClip 容器的形状。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div id="fragment-1725"></div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1730" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,19">
<div class="content-fragment-content">


      <div class="content-fragment-header">示例:</div>
            <div style="margin-bottom: 1em">下面的 VBA 示例使用 PowerClip 容器颜色的浅色调填充当前 PowerClip 容器中的每个形状。</div>
        <div><pre style="code">Sub Test()<br>
 Dim s As Shape<br>
 Dim c As New Color<br>
 If ActiveShape Is Nothing Then<br>
 MsgBox "Select a powerclip container"<br>
 Exit Sub<br>
 End If<br>
 If ActiveShape.Fill.Type &lt;&gt; cdrUniformFill Then<br>
 MsgBox "Powerclip container must have a uniform fill"<br>
 Exit Sub<br>
 End If<br>
 For Each s In ActiveShape.PowerClip.Shapes<br>
 c.CopyAssign s.PowerClipParent.Fill.UniformColor<br>
 Select Case c.Type<br>
 Case cdrColorRGB<br>
 c.RGBBlue = (c.RGBBlue + 255) \ 2<br>
 c.RGBGreen = (c.RGBGreen + 255) \ 2<br>
 c.RGBRed = (c.RGBRed + 255) \ 2<br>
 Case cdrColorCMYK<br>
 c.CMYKBlack = c.CMYKBlack \ 2<br>
 c.CMYKCyan = c.CMYKCyan \ 2<br>
 c.CMYKMagenta = c.CMYKMagenta \ 2<br>
 c.CMYKYellow = c.CMYKYellow \ 2<br>
 Case Else<br>
 c.ConvertToHLS<br>
 c.HLSLightness = (c.HLSLightness + 255) \ 2<br>
 End Select<br>
 s.Fill.ApplyUniformFill c<br>
 Next s<br>
End Sub</pre></div>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
Option Explicit

' 全局常量定义
Public Const trSep As String = "#"
Public Const myVer As Long = 1
Public Const macroName As String = "正则文本查找替换V1.0 by 恒心"
Public Const macroVersion As String = "1.0"

' 全局变量定义
Public sPath As String ' 列表文件路径
Public myListName As String ' 当前活动列表
Public myFormatForC As String
Public FindFormat(5) As String
' Public ChangeFormat(6) As String  ' 已废弃：现在直接检查勾选框状态

' 性能优化开始
Public Sub boostStart(Optional ByVal unDo As String = "")
    On Error Resume Next
    If unDo <> "" Then ActiveDocument.BeginCommandGroup unDo
    Optimization = True
    EventsEnabled = False
    ActiveDocument.SaveSettings
    ActiveDocument.PreserveSelection = False
End Sub

' 性能优化结束
Public Sub boostFinish(Optional ByVal endUndoGroup As Boolean = False)
    On Error Resume Next
    ActiveDocument.PreserveSelection = True
    ActiveDocument.RestoreSettings
    EventsEnabled = True
    Optimization = False
    Application.CorelScript.RedrawScreen
    If endUndoGroup Then ActiveDocument.EndCommandGroup
    Refresh
End Sub

' 主入口函数
Public Sub doReplace()
    If ActiveDocument Is Nothing Then Exit Sub
    ' 显示窗体
    正则窗体.Show 0
End Sub
<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>Shape.Separate</title> 
                        </head><body>
                        <div><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><a href="./Shape.html" class="internal-link"><strong>Shape</strong></a></div>
<div class="content-fragment-header"><a href="./Shape.Separate.html" class="internal-link"><strong>Shape.Separate</strong></a></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">分隔已组合或来自链接组的形状</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                                                                                                                                    <div class="content-fragment-header">语法:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
      <div class="code-block"><span class="keyword">Sub</span><span class="space"> </span> <span class="identifier">Separate</span><span class="punctuation">(</span><span class="punctuation">)</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>Separate</b> 方法分离效果形状(即组合形状或链接组,例如混合组或投影)。</div>
                <div>出于兼容性原因提供此方法。建议的方法是 <a href="./Effect.Separate.html">Effect.Separate.</a></div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div id="fragment-1725"></div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1730" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,19">
<div class="content-fragment-content">


      <div class="content-fragment-header">示例:</div>
            <div style="margin-bottom: 1em">以下 VBA 示例在两个椭圆之间创建混合并将其分开。</div>
        <div><pre style="code">Sub Test()<br>
 Dim s1 As Shape, s2 As Shape<br>
 Set s1 = ActiveLayer.CreateEllipse(0, 10, 2, 8)<br>
 Set s2 = s1.Duplicate(5, -7)<br>
 s1.Fill.UniformColor.CMYKAssign 0, 0, 100, 0<br>
 s2.Fill.UniformColor.CMYKAssign 0, 100, 100, 0<br>
 s2.CreateBlend(s1).Blend.BlendGroup.CreateSelection<br>
 s2.Selected = True<br>
 s1.Selected = True<br>
 ActiveSelection.Separate<br>
End Sub</pre></div>
            <div style="margin-bottom: 1em">以下 VBA 示例执行与前面示例相同的过程,但使用 <a href="./Effect.Separate.html">Effect.Separate</a> 方法。</div>
        <div style="margin-bottom: 1em">Sub Test()</div>
        <div style="margin-bottom: 1em"> Dim s1 As Shape, s2 As Shape</div>
        <div style="margin-bottom: 1em"> 设置 s1 = ActiveLayer.CreateEllipse(0, 10, 2, 8)</div>
        <div style="margin-bottom: 1em"> 设置 s2 = s1.Duplicate(5, -7)</div>
        <div style="margin-bottom: 1em"> s1.Fill.UniformColor.CMYKAssign 0, 0, 100, 0</div>
        <div style="margin-bottom: 1em"> s2.Fill.UniformColor.CMYKAssign 0, 100, 100, 0</div>
        <div style="margin-bottom: 1em"> s2.CreateBlend(s1).Separate</div>
        <div style="margin-bottom: 1em">End Sub</div>
        <div><pre style="code"></pre></div>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
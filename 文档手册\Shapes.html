<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>Shapes</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./Shapes.html" class="internal-link">Shapes</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>Shapes class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">表示形状( Shape 对象)的集合</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">Shapes</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>Shapes </b> 类表示形状(<a href="./Shape.html">Shape</a>对象)的集合。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./Shapes._NewEnum.html" class="internal-link">_NewEnum</a></td>
          <td>_New枚举</td>
          <td>返回一个遍历集合的枚举器</td>
        </tr>
            <tr>
          <td><a href="./Shapes.Application.html" class="internal-link">Application</a></td>
          <td>应用</td>
          <td>获取形状集合所属的应用程序</td>
        </tr>
            <tr>
          <td><a href="./Shapes.Count.html" class="internal-link">Count</a></td>
          <td>数数</td>
          <td>获取集合中的形状数量</td>
        </tr>
            <tr>
          <td><a href="./Shapes.First.html" class="internal-link">First</a></td>
          <td>第一的</td>
          <td>返回集合中的第一个形状</td>
        </tr>
            <tr>
          <td><a href="./Shapes.Item.html" class="internal-link">Item</a></td>
          <td>物品</td>
          <td>获取对指定形状的引用,以 1 为基数</td>
        </tr>
            <tr>
          <td><a href="./Shapes.Last.html" class="internal-link">Last</a></td>
          <td>最后的</td>
          <td>返回集合中的最后一个形状</td>
        </tr>
            <tr>
          <td><a href="./Shapes.Parent.html" class="internal-link">Parent</a></td>
          <td>家长</td>
          <td>获取形状集合是其子级的父级</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1726" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,15">
<div class="content-fragment-content">


      <div class="content-fragment-header">方法:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./Shapes.All.html" class="internal-link">All</a></td>
          <td>全部</td>
          <td>获取形状范围</td>
        </tr>
            <tr>
          <td><a href="./Shapes.AllExcluding.html" class="internal-link">AllExcluding</a></td>
          <td>全部不包括</td>
          <td>获取不包括索引的形状范围</td>
        </tr>
            <tr>
          <td><a href="./Shapes.FindShape.html" class="internal-link">FindShape</a></td>
          <td>查找形状</td>
          <td>获取基于指定属性的形状。</td>
        </tr>
            <tr>
          <td><a href="./Shapes.FindShapes.html" class="internal-link">FindShapes</a></td>
          <td>查找形状</td>
          <td>根据指定的属性查找形状并将它们作为形状范围返回</td>
        </tr>
            <tr>
          <td><a href="./Shapes.Range.html" class="internal-link">Range</a></td>
          <td>范围</td>
          <td>从索引获取形状范围</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
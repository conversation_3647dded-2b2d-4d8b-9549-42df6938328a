<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>Shape.OverprintFill</title> 
                        </head><body>
                        <div><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><a href="./Shape.html" class="internal-link"><strong>Shape</strong></a></div>
<div class="content-fragment-header"><a href="./Shape.OverprintFill.html" class="internal-link"><strong>Shape.OverprintFill</strong></a></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">返回或设置是否应叠印填充</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <div class="content-fragment-header">语法:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
      <div class="code-block"><span class="keyword">Property</span><span class="space"> </span> <span class="keyword">Get</span><span class="space"> </span> <span class="identifier">OverprintFill</span><span class="punctuation">(</span><span class="punctuation">)</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Boolean</span><br>
<span class="keyword">Property</span><span class="space"> </span> <span class="keyword">Let</span><span class="space"> </span> <span class="identifier">OverprintFill</span><span class="punctuation">(</span><span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">Value</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Boolean</span><span class="punctuation">)</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>OverprintFill</b> 属性返回或指定是否叠印对象的填充。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div id="fragment-1725"></div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1730" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,19">
<div class="content-fragment-content">


      <div class="content-fragment-header">示例:</div>
            <div style="margin-bottom: 1em">以下 VBA 示例通过创建两个或多个形状相交的附加对象来预览图形中的叠印填充。这些新对象填充了适当的结果颜色。文档中必须至少有两个重叠的形状。</div>
        <div><pre style="code">' Previews fill overprints on screen<br>
Public Sub CreateOverprint()<br>
 Dim n1 As Long<br>
 Dim n2 As Long<br>
 Dim s1 As Shape<br>
 Dim s2 As Shape<br>
 Dim s As Shape<br>
 Dim shps As Shapes<br>
 Dim c1 As New color<br>
 Dim c2 As New color<br>
 ActiveDocument.ReferencePoint = cdrBottomLeft<br>
 ActiveDocument.ShapeEnumDirection = cdrShapeEnumBottomFirst<br>
 ' Look through all shapes from bottom to top<br>
 Set shps = ActivePage.Shapes<br>
 For n1 = 1 To shps.Count - 1<br>
 Set s1 = shps(n1)<br>
 If s1.Fill.Type = cdrUniformFill Then<br>
 ' If the shape has a uniform fill, get its color<br>
 c1.CopyAssign s1.Fill.UniformColor<br>
 ' Check all shapes above it<br>
 For n2 = n1 + 1 To shps.Count<br>
 Set s2 = shps(n2)<br>
 If s2.Fill.Type = cdrUniformFill And s2.OverprintFill Then<br>
 ' If the shape has a uniform fill has Overprint fill specified,<br>
 ' get its color<br>
 c2.CopyAssign s2.Fill.UniformColor<br>
 If Overlap(s1, s2) Then<br>
 ' If the shapes may overlap, mix the two colors and ...<br>
 MixColors c1, c2<br>
 ' ... create the intersecting shape<br>
 Set s = s1.Intersect(s2)<br>
 If Not s Is Nothing Then<br>
 ' If anything was generated during intersection,<br>
 ' apply the resulting color to it and mark the shape with<br>
 ' overprint fill attribute for future processing<br>
 s.Fill.ApplyUniformFill c2<br>
 s.OverprintFill = True<br>
 End If<br>
 End If<br>
 End If<br>
 Next n2<br>
 End If<br>
 Next n1<br>
End Sub<br>
' Determines if the two shapes may overlap<br>
Private Function Overlap(s1 As Shape, s2 As Shape) As Boolean<br>
 Dim x1 As Double, y1 As Double, w1 As Double, h1 As Double<br>
 Dim x2 As Double, y2 As Double, w2 As Double, h2 As Double<br>
 s1.GetBoundingBox x1, y1, w1, h1<br>
 s2.GetBoundingBox x2, y2, w2, h2<br>
 Overlap = Not (x1 + w1 &lt; x2 Or x2 + w2 &lt; x1 Or y1 + h1 &lt; y2 Or y2 + h2 &lt; y1)<br>
End Function<br>
' Mixes two colors according to their inks<br>
Private Sub MixColors(c1 As color, c2 As color)<br>
 Dim cc1 As New color<br>
 Dim bSpot As Boolean<br>
 cc1.CopyAssign c1<br>
 If cc1.Type &lt;&gt; cdrColorCMYK Then cc1.ConvertToCMYK<br>
 bSpot = (c1.Type = cdrColorSpot Or c1.Type = cdrColorPantone Or _<br>
 c2.Type = cdrColorSpot Or c2.Type = cdrColorPantone)<br>
 If c2.Type &lt;&gt; cdrColorCMYK Then c2.ConvertToCMYK<br>
 If Not bSpot Then<br>
 ' If we are mixing process colors, only replace the color channels that<br>
 ' have no color in the top shape<br>
 If c2.CMYKBlack = 0 Then c2.CMYKBlack = cc1.CMYKBlack<br>
 If c2.CMYKCyan = 0 Then c2.CMYKCyan = cc1.CMYKCyan<br>
 If c2.CMYKMagenta = 0 Then c2.CMYKMagenta = cc1.CMYKMagenta<br>
 If c2.CMYKYellow = 0 Then c2.CMYKYellow = cc1.CMYKYellow<br>
 Else<br>
 ' If we are mixing spot colors, just add inks<br>
 c2.CMYKBlack = GetMaxInk(cc1.CMYKBlack + c2.CMYKBlack)<br>
 c2.CMYKCyan = GetMaxInk(cc1.CMYKCyan + c2.CMYKCyan)<br>
 c2.CMYKMagenta = GetMaxInk(cc1.CMYKMagenta + c2.CMYKMagenta)<br>
 c2.CMYKYellow = GetMaxInk(cc1.CMYKYellow + c2.CMYKYellow)<br>
 End If<br>
 End Sub<br>
 ' Makes sure the ink level doesn't exceed 100%<br>
 Private Function GetMaxInk(Ink As Long) As Long<br>
 Dim n As Long<br>
 n = Ink<br>
 If n &gt; 100 Then n = 100<br>
 GetMaxInk = n<br>
 End Function</pre></div>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>Shape.StaticID</title> 
                        </head><body>
                        <div><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><a href="./Shape.html" class="internal-link"><strong>Shape</strong></a></div>
<div class="content-fragment-header"><a href="./Shape.StaticID.html" class="internal-link"><strong>Shape.StaticID</strong></a></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">返回对象数据管理器的静态 ID 字段</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <div class="content-fragment-header">语法:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
      <div class="code-block"><span class="keyword">Property</span><span class="space"> </span> <span class="keyword">Get</span><span class="space"> </span> <span class="identifier">StaticID</span><span class="punctuation">(</span><span class="punctuation">)</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Long</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>StaticID</b> 属性返回一个整数,表示分配给每个形状的唯一 ID 号。这个数字不会改变,因此可以在形状的整个生命周期中使用。</div>
                <div>您可以使用 <a href="./Page.FindShape.html">Page.FindShape</a> 或 <a href="./Layer.FindShape.html">Layer.FindShape</a> 方法来定位基于其唯一 ID 的形状。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div id="fragment-1725"></div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1730" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,19">
<div class="content-fragment-content">


      <div class="content-fragment-header">示例:</div>
            <div style="margin-bottom: 1em">以下 VBA 示例将当前选择存储在活动页面属性的数组中。</div>
        <div><pre style="code">Sub SaveSelection()<br>
 Dim s As Shape, n As Long<br>
 ActivePage.Properties("StoredSelection", 0) = ActiveSelection.Shapes.Count<br>
 n = 1<br>
 For Each s In ActiveSelection.Shapes<br>
 ActivePage.Properties("StoredSelection", n) = s.StaticID ' Store the current shape's ID number<br>
 n = n + 1<br>
 Next s<br>
End Sub</pre></div>
            <div style="margin-bottom: 1em">下面的 VBA 示例恢复了上一个示例中活动页面的属性中保存的选择。</div>
        <div><pre style="code">Sub RestoreSelection()<br>
 Dim s As Shape, sr As New ShapeRange<br>
 Dim v As Variant<br>
 Dim Num As Long, i As Long, id As Long<br>
 v = ActivePage.Properties("StoredSelection", 0) ' Retrieving the total number of shape references stored<br>
 If Not IsNull(v) Then<br>
 ActivePage.Properties.Delete "StoredSelection", 0 ' Delete the property<br>
 Num = v<br>
 For i = 1 To Num<br>
 id = ActivePage.Properties("StoredSelection", i) ' Getting the current shape's ID to find<br>
 Set s = ActivePage.FindShape(StaticID:=id)<br>
 If Not s Is Nothing Then sr.Add s ' Add the shape to the shape range if it still exists in document<br>
 ActivePage.Properties.Delete "StoredSelection", i ' Delete the property<br>
 Next i<br>
 sr.CreateSelection ' Selects all shapes found<br>
 Else<br>
 MsgBox "No selection stored in the active page"<br>
 End If<br>
 End Sub</pre></div>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
# CQL查询优化说明

## 概述
根据您提供的CQL（Corel查询语言）文档，我已经将原来使用FOR循环逐步查找文本对象的代码改为使用CQL查询方式，提高了查找效率。

## 主要改进

### 1. myFindShapes函数优化
**原来的实现：**
```vba
' 使用FOR循环遍历所有形状
For Each s In p.Shapes.All
    If s.Type = cdrTextShape Then
        If s.Text.Type = cdrParagraphText Then
            shapes.Add s
        End If
    End If
Next s
```

**优化后的实现：**
```vba
' 使用CQL查询直接查找文本对象
cqlQuery = "@type = 'text'"
Set pageShapes = ActivePage.Shapes.FindShapes(Query:=cqlQuery)

' 然后过滤出段落文本
For Each s In pageShapes
    If s.Text.Type = cdrParagraphText Then
        allShapes.AddRange s
    End If
Next s
```

### 2. CQL语法应用
根据您提供的文档，我使用了以下CQL语法：

- **基本查询：** `@type = 'text'` - 查找所有文本对象
- **属性访问：** 使用`@`符号访问对象属性
- **FindShapes方法：** `Shapes.FindShapes(Query:=cqlQuery)` - 执行CQL查询

### 3. 性能优化效果

**优化前：**
- 需要遍历页面上的所有形状（包括矩形、椭圆、线条等）
- 对每个形状都要检查类型
- 效率较低，特别是在形状数量多的情况下

**优化后：**
- 直接通过CQL查询筛选出文本对象
- 减少了不必要的类型检查
- 提高了查找效率

### 4. 支持的查找范围

优化后的代码支持三种查找范围：

1. **当前文档：** 在所有页面中使用CQL查询查找文本对象
2. **当前页面：** 在当前页面中使用CQL查询查找文本对象  
3. **当前选择：** 在选中的对象中使用CQL查询筛选文本对象

### 5. 调试信息增强

添加了详细的调试信息：
```vba
Debug.Print "CQL查询语句: " & cqlQuery
Debug.Print "在页面 " & p.Name & " 找到 " & pageShapes.Count & " 个文本对象"
Debug.Print "CQL查询结果: 找到 " & myFindShapes.Count & " 个段落文本形状"
```

## 技术细节

### CQL查询语法参考
根据您提供的文档：

- `@type = 'text'` - 查找文本对象
- `@width > (2 in)` - 宽度大于2英寸的对象
- `@fill.type = 'uniform'` - 具有均匀填充的对象
- `@outline.type = 'solid'` - 具有实线轮廓的对象

### 段落文本过滤
由于CQL查询返回所有文本对象（包括艺术文本和段落文本），我们仍需要在结果中过滤出段落文本：
```vba
If s.Text.Type = cdrParagraphText Then
    allShapes.AddRange s
End If
```

## 兼容性说明

- 该优化保持了原有功能的完整性
- 所有现有的替换、格式设置功能都不受影响
- 仅优化了文本对象的查找方式

## 未来可能的进一步优化

如果CorelDRAW的CQL支持更详细的文本类型查询，可以考虑：
```vba
' 理论上的进一步优化（需要验证是否支持）
cqlQuery = "@type = 'text' and @text.type = 'paragraph'"
```

这样可以直接在CQL查询中指定段落文本，进一步减少后续的过滤工作。

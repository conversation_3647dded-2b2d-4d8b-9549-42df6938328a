<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>Shape.CreateContour</title> 
                        </head><body>
                        <div><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><a href="./Shape.html" class="internal-link"><strong>Shape</strong></a></div>
<div class="content-fragment-header"><a href="./Shape.CreateContour.html" class="internal-link"><strong>Shape.CreateContour</strong></a></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">创建轮廓效果</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <div class="content-fragment-header">语法:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
      <div class="code-block"><span class="keyword">Function</span><span class="space"> </span> <span class="identifier">CreateContour</span><span class="punctuation">(</span><span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">Direction</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./cdrContourDirection.html" class="internal-link">cdrContourDirection</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="namespace">cdrContourOutside</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">Offset</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Double</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="number">0</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">Steps</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Long</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="number">1</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">BlendType</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./cdrFountainFillBlendType.html" class="internal-link">cdrFountainFillBlendType</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="namespace">cdrDirectFountainFillBlend</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">OutlineColor</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./Color.html" class="internal-link">Color</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="keyword">Nothing</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">FillColor</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./Color.html" class="internal-link">Color</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="keyword">Nothing</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">FillColor2</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./Color.html" class="internal-link">Color</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="keyword">Nothing</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">SpacingAccel</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Long</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="number">0</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">ColorAccel</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Long</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="number">0</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">EndCapType</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="https://community.coreldraw.com/sdk/api/draw/17/e/cdrContourEndCapType.html" class="internal-link">cdrContourEndCapType</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="namespace">cdrContourSquareCap</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">CornerType</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="https://community.coreldraw.com/sdk/api/draw/17/e/cdrContourCornerType.html" class="internal-link">cdrContourCornerType</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="namespace">cdrContourCornerMiteredOffsetBevel</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">MiterLimit</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Double</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="number">0</span><span class="punctuation">)</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./Effect.html" class="internal-link">Effect</a></span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1721" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,10">
<div class="content-fragment-content">


      <div class="content-fragment-header">参数:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名称</th>
          <th>类型</th>
          <th>描述</th>
        </tr>
            <tr>
          <td>Direction</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./cdrContourDirection.html" class="internal-link">cdrContourDirection</a></span></div>
                      </td>
          <td><div>指定轮廓的方向,并返回 <a href="./cdrContourDirection.html">cdrContourDirection</a>。</div></td>
        </tr>
            <tr>
          <td>Offset</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Double</span></div>
                      </td>
          <td><div>以文档单位指定轮廓线之间的偏移距离,自动调整轮廓步数。</div></td>
        </tr>
            <tr>
          <td>Steps</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Long</span></div>
                      </td>
          <td><div>指定步数,该步数与??效果中出现的行数相关联。</div></td>
        </tr>
            <tr>
          <td>BlendType</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./cdrFountainFillBlendType.html" class="internal-link">cdrFountainFillBlendType</a></span></div>
                      </td>
          <td><div>指定颜色混合类型,并返回 <a href="./cdrFountainFillBlendType.html">cdrFountainFillBlendType</a>。</div></td>
        </tr>
            <tr>
          <td>OutlineColor</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./Color.html" class="internal-link">Color</a></span></div>
                      </td>
          <td><div>指定轮廓的轮廓颜色。</div></td>
        </tr>
            <tr>
          <td>FillColor</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./Color.html" class="internal-link">Color</a></span></div>
                      </td>
          <td><div>指定轮廓的填充颜色。</div></td>
        </tr>
            <tr>
          <td>FillColor2</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./Color.html" class="internal-link">Color</a></span></div>
                      </td>
          <td><div>指定 <a href="./EffectContour.FillColorTo.html">EffectContour.FillColorTo</a> 颜色。</div></td>
        </tr>
            <tr>
          <td>SpacingAccel</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Long</span></div>
                      </td>
          <td><div>指定步长的变化。</div></td>
        </tr>
            <tr>
          <td>ColorAccel</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Long</span></div>
                      </td>
          <td><div>指定颜色变化的加速度。</div></td>
        </tr>
            <tr>
          <td>EndCapType</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./cdrContourEndCapType.html" class="internal-link">cdrContourEndCapType</a></span></div>
                      </td>
          <td><div></div></td>
        </tr>
            <tr>
          <td>CornerType</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./cdrContourCornerType.html" class="internal-link">cdrContourCornerType</a></span></div>
                      </td>
          <td><div></div></td>
        </tr>
            <tr>
          <td>MiterLimit</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Double</span></div>
                      </td>
          <td><div></div></td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>CreateContour</b> 方法将轮廓应用于形状,返回表示轮廓属性的 <a href="./Effect.html">Effect</a> 对象。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div id="fragment-1725"></div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1730" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,19">
<div class="content-fragment-content">


      <div class="content-fragment-header">示例:</div>
            <div style="margin-bottom: 1em">下面的 VBA 示例创建一个文本字符串并对其应用轮廓效果,从而导致外观模糊。</div>
        <div><pre style="code">Sub Test()<br>
 Dim sText As Shape<br>
 Set sText = ActiveLayer.CreateArtisticText(4, 5, "Blurred Text")<br>
 sText.Fill.UniformColor.RGBAssign 0, 0, 0<br>
 With sText.Text.FontProperties<br>
 .Name = "Arial"<br>
 .Size = 90<br>
 .Style = cdrBoldFontStyle<br>
 End With<br>
 sText.Text.AlignProperties.Alignment = cdrCenterAlignment<br>
 sText.CreateContour cdrContourOutside, 0.01, 10, , , CreateRGBColor(255, 255, 255)<br>
End Sub</pre></div>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>StructImportOptions</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./StructImportOptions.html" class="internal-link">StructImportOptions</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>StructImportOptions class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">StructImportOptions 类</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">StructImportOptions</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div id="fragment-1723"></div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.CodePage.html" class="internal-link">CodePage</a></td>
          <td>代码页</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.ColorConversionOptions.html" class="internal-link">ColorConversionOptions</a></td>
          <td>颜色转换选项</td>
          <td>指定导入的颜色转换选项</td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.CombineMultilayerBitmaps.html" class="internal-link">CombineMultilayerBitmaps</a></td>
          <td>组合多层位图</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.ConvertTablesToText.html" class="internal-link">ConvertTablesToText</a></td>
          <td>将表格转换为文本</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.CropHandler.html" class="internal-link">CropHandler</a></td>
          <td>作物处理程序</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.CropHeight.html" class="internal-link">CropHeight</a></td>
          <td>裁剪高度</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.CropLeft.html" class="internal-link">CropLeft</a></td>
          <td>左裁剪</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.CropTop.html" class="internal-link">CropTop</a></td>
          <td>作物顶部</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.CropWidth.html" class="internal-link">CropWidth</a></td>
          <td>裁剪宽度</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.CustomData.html" class="internal-link">CustomData</a></td>
          <td>自定义数据</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.DetectWatermark.html" class="internal-link">DetectWatermark</a></td>
          <td>检测水印</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.ExtractICCProfile.html" class="internal-link">ExtractICCProfile</a></td>
          <td>提取ICC配置文件</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.ForceCMYKBlackText.html" class="internal-link">ForceCMYKBlackText</a></td>
          <td>ForceCMYK黑色文本</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.ICCFileName.html" class="internal-link">ICCFileName</a></td>
          <td>ICC文件名</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.ImageIndex.html" class="internal-link">ImageIndex</a></td>
          <td>图像索引</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.LinkBitmapExternally.html" class="internal-link">LinkBitmapExternally</a></td>
          <td>外部链接位图</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.MaintainLayers.html" class="internal-link">MaintainLayers</a></td>
          <td>维护层</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.Mode.html" class="internal-link">Mode</a></td>
          <td>模式</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.ResampleDpiX.html" class="internal-link">ResampleDpiX</a></td>
          <td>重采样DpiX</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.ResampleDpiY.html" class="internal-link">ResampleDpiY</a></td>
          <td>重采样DpiY</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.ResampleHandler.html" class="internal-link">ResampleHandler</a></td>
          <td>重采样处理程序</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.ResampleHeight.html" class="internal-link">ResampleHeight</a></td>
          <td>重采样高度</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.ResampleWidth.html" class="internal-link">ResampleWidth</a></td>
          <td>重采样宽度</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.TableColumnDelimiter.html" class="internal-link">TableColumnDelimiter</a></td>
          <td>表列分隔符</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.TextFormatting.html" class="internal-link">TextFormatting</a></td>
          <td>文本格式</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructImportOptions.UseOPILinks.html" class="internal-link">UseOPILinks</a></td>
          <td>使用 OPI 链接</td>
          <td></td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
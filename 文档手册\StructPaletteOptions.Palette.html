<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>StructPaletteOptions.Palette</title> 
                        </head><body>
                        <div><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><a href="./StructPaletteOptions.html" class="internal-link"><strong>StructPaletteOptions</strong></a></div>
<div class="content-fragment-header"><a href="./StructPaletteOptions.Palette.html" class="internal-link"><strong>StructPaletteOptions.Palette</strong></a></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;"></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <div class="content-fragment-header">语法:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
      <div class="code-block"><span class="keyword">Property</span><span class="space"> </span> <span class="keyword">Get</span><span class="space"> </span> <span class="identifier">Palette</span><span class="punctuation">(</span><span class="punctuation">)</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Variant</span><br>
<span class="keyword">Property</span><span class="space"> </span> <span class="keyword">Let</span><span class="space"> </span> <span class="identifier">Palette</span><span class="punctuation">(</span><span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">Value</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Variant</span><span class="punctuation">)</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>Palette</b> 属性在将全色图像导出到调色位图时指定调色板。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div id="fragment-1725"></div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1730" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,19">
<div class="content-fragment-content">


      <div class="content-fragment-header">示例:</div>
            <div style="margin-bottom: 1em">以下 VBA 示例使用新的自定义调色板导出文档:</div>
        <div><pre style="code">Sub Test()<br>
 Dim d As Document<br>
 Dim e As ExportFilter<br>
 Dim s As Shape<br>
 Dim sp As New StructPaletteOptions<br>
 Dim p As Palette<br>
 Set d = CreateDocument<br>
 Set s = d.ActiveLayer.CreateRectangle(3, 3, 7, 7)<br>
 s.Fill.ApplyFountainFill CreateRGBColor(255, 0, 0), CreateRGBColor(0, 0, 255)<br>
 ' Change the palette type to custom.<br>
 sp.PaletteType = cdrPaletteCustom<br>
 ' Create a new palette and add some colors.<br>
 Set p = Palettes.Create("Temp")<br>
 p.AddColor CreateRGBColor(255, 0, 0)<br>
 p.AddColor CreateRGBColor(0, 0, 255)<br>
 p.AddColor CreateRGBColor(0, 255, 0)<br>
 ' Set the palette to use for the export to the newly created palette.<br>
 sp.Palette = p<br>
 d.Export "C:\Test_ExportPalette_Passing_a_Palette.gif", cdrGIF, , , sp<br>
End Sub</pre></div>
            <div style="margin-bottom: 1em">以下 VBA 示例导出具有自定义调色板的文档,该调色板由其路径和文件名标识:</div>
        <div><pre style="code">Sub Test()<br>
 Dim d As Document<br>
 Dim e As ExportFilter<br>
 Dim s As Shape<br>
 Dim sp As New StructPaletteOptions<br>
 Set d = CreateDocument<br>
 Set s = d.ActiveLayer.CreateRectangle(3, 3, 7, 7)<br>
 s.Fill.ApplyFountainFill CreateRGBColor(255, 0, 0), CreateRGBColor(0, 0, 255)<br>
 ' Change the palette type to custom.<br>
 sp.PaletteType = cdrPaletteCustom<br>
 ' Set the palette to use for the export to the filename of the existing palette.<br>
 sp.Palette = SetupPath &amp; "Custom Data\Palettes\CMYK\Nature\cEarthy.xml"<br>
 d.Export "C:\Test_ExportPalette_Passing_a_Filename.gif", cdrGIF, , , sp<br>
End Sub</pre></div>
            <div style="margin-bottom: 1em">以下 VBA 示例导出具有自定义调色板的文档,该调色板由长值数组(表示颜色)定义:</div>
        <div><pre style="code">Sub Test()<br>
 Dim d As Document<br>
 Dim e As ExportFilter<br>
 Dim s As Shape<br>
 Dim sp As New StructPaletteOptions<br>
 Set d = CreateDocument<br>
 Set s = d.ActiveLayer.CreateRectangle(3, 3, 7, 7)<br>
 s.Fill.ApplyFountainFill CreateRGBColor(255, 0, 0), CreateRGBColor(0, 0, 255)<br>
 ' Change the palette type to custom.<br>
 sp.PaletteType = cdrPaletteCustom<br>
 ' Set the palette to use for the export to an array of longs which represent the colors to use.<br>
 sp.Palette = Array(RGB(255, 0, 0), RGB(0, 0, 255), RGB(125, 0, 0), RGB(0, 0, 125), RGB(155, 0, 0), RGB(0, 0, 155), RGB(175, 32, 175))<br>
 d.Export "C:\Test_ExportPalette_Passing_an_array_of_longs.gif", cdrGIF, , , sp<br>
End Sub</pre></div>
            <div style="margin-bottom: 1em">以下 VBA 示例导出具有自定义调色板的文档,该调色板由颜色数组定义:</div>
        <div><pre style="code">Sub Test()<br>
 Dim d As Document<br>
 Dim e As ExportFilter<br>
 Dim s As Shape<br>
 Dim sp As New StructPaletteOptions<br>
 Set d = CreateDocument<br>
 Set s = d.ActiveLayer.CreateRectangle(3, 3, 7, 7)<br>
 s.Fill.ApplyFountainFill CreateRGBColor(255, 0, 0), CreateRGBColor(0, 0, 255)<br>
 ' Change the palette type to custom<br>
 sp.PaletteType = cdrPaletteCustom<br>
 ' Set the palette to use for the export to an array of colors<br>
 sp.Palette = Array(CreateCMYKColor(0, 77, 23, 23), CreateCMYKColor(10, 23, 43, 54), CreateCMYKColor(54, 12, 88, 12))<br>
 d.Export "C:\Test_ExportPalette_Passing_an_array_of_colors.gif", cdrGIF, , , sp<br>
End Sub</pre></div>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
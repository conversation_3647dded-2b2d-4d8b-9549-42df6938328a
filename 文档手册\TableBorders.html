<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>TableBorders</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./TableBorders.html" class="internal-link">TableBorders</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>TableBorders class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;"></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">TableBorders</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>TableBorders</b> 类表示表格的边框设置。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./TableBorders.All.html" class="internal-link">All</a></td>
          <td>全部</td>
          <td>指定所有边框轮廓的属性</td>
        </tr>
            <tr>
          <td><a href="./TableBorders.AreIdentical.html" class="internal-link">AreIdentical</a></td>
          <td>是相同的</td>
          <td>检查给定边框的轮廓是否相同</td>
        </tr>
            <tr>
          <td><a href="./TableBorders.Bottom.html" class="internal-link">Bottom</a></td>
          <td>底部</td>
          <td>指定底部边框轮廓</td>
        </tr>
            <tr>
          <td><a href="./TableBorders.Combined.html" class="internal-link">Combined</a></td>
          <td>结合</td>
          <td>指定一组边框轮廓的属性</td>
        </tr>
            <tr>
          <td><a href="./TableBorders.Horizontal.html" class="internal-link">Horizontal</a></td>
          <td>水平的</td>
          <td>指定水平内边框轮廓</td>
        </tr>
            <tr>
          <td><a href="./TableBorders.Left.html" class="internal-link">Left</a></td>
          <td>剩下</td>
          <td>指定左边框轮廓</td>
        </tr>
            <tr>
          <td><a href="./TableBorders.Right.html" class="internal-link">Right</a></td>
          <td>对</td>
          <td>指定右边框轮廓</td>
        </tr>
            <tr>
          <td><a href="./TableBorders.Top.html" class="internal-link">Top</a></td>
          <td>最佳</td>
          <td>指定上边框轮廓</td>
        </tr>
            <tr>
          <td><a href="./TableBorders.Vertical.html" class="internal-link">Vertical</a></td>
          <td>垂直的</td>
          <td>指定垂直内边框轮廓</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1726" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,15">
<div class="content-fragment-content">


      <div class="content-fragment-header">方法:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./TableBorders.SetBorders.html" class="internal-link">SetBorders</a></td>
          <td>设置边框</td>
          <td>设置给定边框的轮廓属性</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>StructImportCropOptions.FilterID</title> 
                        </head><body>
                        <div><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><a href="./StructImportCropOptions.html" class="internal-link"><strong>StructImportCropOptions</strong></a></div>
<div class="content-fragment-header"><a href="./StructImportCropOptions.FilterID.html" class="internal-link"><strong>StructImportCropOptions.FilterID</strong></a></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;"></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <div class="content-fragment-header">语法:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
      <div class="code-block"><span class="keyword">Property</span><span class="space"> </span> <span class="keyword">Get</span><span class="space"> </span> <span class="identifier">FilterID</span><span class="punctuation">(</span><span class="punctuation">)</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Long</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>FilterID</b> 属性指定导入时作物的过滤器 ID。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div id="fragment-1725"></div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1730" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,19">
<div class="content-fragment-content">


      <div class="content-fragment-header">示例:</div>
            <div style="margin-bottom: 1em">以下 VBA 示例创建一个新的裁剪选项类。然后它会导入一个 JPEG 文件并根据 <a href="./StructImportCropOptions.CustomData.html">StructImportCropOptions.CustomData</a> 的值对其进行裁剪。在应用裁剪之前，会显示图像的原始设置。</div>
        <div style="margin-bottom: 1em">要试用此代码示例,请将以下代码行复制到名为 <b>cCrop</b> 的新类模块中:</div>
        <div><pre style="code">Option Explicit<br>
Implements IImportCropHandler<br>
Private Function IImportCropHandler_Crop(Options As IStructImportCropOptions) As Boolean<br>
 Dim s As String<br>
 s = "Original Settings: " &amp; vbCr<br>
 s = s &amp; "DpiX: " &amp; Options.DpiX &amp; vbCr<br>
 s = s &amp; "DpiY: " &amp; Options.DpiY &amp; vbCr<br>
 s = s &amp; "FileName: " &amp; Options.FileName &amp; vbCr<br>
 s = s &amp; "Filter: " &amp; Options.FilterID &amp; vbCr<br>
 s = s &amp; "ImageHeight: " &amp; Options.ImageHeight &amp; vbCr<br>
 s = s &amp; "ImageWidth: " &amp; Options.ImageWidth &amp; vbCr<br>
 ' Display the original settings in a message box<br>
 MsgBox s<br>
 ' If the custom data is set to 7 then crop half of the original; otherwise, subtract 0.5 units from the height and width.<br>
 If Options.CustomData = 7 Then<br>
 Options.Height = Options.Height / 2<br>
 Options.Width = Options.Width / 2<br>
 Options.Top = Options.Top / 2<br>
 Options.Left = Options.Left / 2<br>
 Else<br>
 Options.Height = Options.Height - 0.5<br>
 Options.Width = Options.Width - 0.5<br>
 End If<br>
End Function</pre></div>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
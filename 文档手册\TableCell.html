<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>TableCell</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./TableCell.html" class="internal-link">TableCell</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>TableCell class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;"></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">TableCell</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>TableCell</b> 类表示表格单元格的设置。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./TableCell.BorderCurve.html" class="internal-link">BorderCurve</a></td>
          <td>边界曲线</td>
          <td>返回单元格边界曲线</td>
        </tr>
            <tr>
          <td><a href="./TableCell.Borders.html" class="internal-link">Borders</a></td>
          <td>边框</td>
          <td>返回单元格的边框属性</td>
        </tr>
            <tr>
          <td><a href="./TableCell.BottomMargin.html" class="internal-link">BottomMargin</a></td>
          <td>底边距</td>
          <td>指定单元格的底部文本边距</td>
        </tr>
            <tr>
          <td><a href="./TableCell.CellAbove.html" class="internal-link">CellAbove</a></td>
          <td>单元格上方</td>
          <td>返回当前单元格上方的单元格</td>
        </tr>
            <tr>
          <td><a href="./TableCell.CellBelow.html" class="internal-link">CellBelow</a></td>
          <td>单元格下方</td>
          <td>返回当前单元格下方的单元格</td>
        </tr>
            <tr>
          <td><a href="./TableCell.CellToLeft.html" class="internal-link">CellToLeft</a></td>
          <td>向左单元格</td>
          <td>返回当前单元格左侧的单元格</td>
        </tr>
            <tr>
          <td><a href="./TableCell.CellToRight.html" class="internal-link">CellToRight</a></td>
          <td>向右单元格</td>
          <td>返回当前单元格右侧的单元格</td>
        </tr>
            <tr>
          <td><a href="./TableCell.Column.html" class="internal-link">Column</a></td>
          <td>柱子</td>
          <td>返回当前单元格所属的列</td>
        </tr>
            <tr>
          <td><a href="./TableCell.ColumnIndex.html" class="internal-link">ColumnIndex</a></td>
          <td>列索引</td>
          <td>返回当前单元格所属列的索引</td>
        </tr>
            <tr>
          <td><a href="./TableCell.ColumnSpan.html" class="internal-link">ColumnSpan</a></td>
          <td>列跨度</td>
          <td>返回单元格跨越的列数</td>
        </tr>
            <tr>
          <td><a href="./TableCell.EqualMargins.html" class="internal-link">EqualMargins</a></td>
          <td>等边距</td>
          <td>如果单元格的所有四个文本边距相同,则返回 True</td>
        </tr>
            <tr>
          <td><a href="./TableCell.Fill.html" class="internal-link">Fill</a></td>
          <td>充满</td>
          <td>返回单元格的填充</td>
        </tr>
            <tr>
          <td><a href="./TableCell.Height.html" class="internal-link">Height</a></td>
          <td>高度</td>
          <td>返回单元格的高度</td>
        </tr>
            <tr>
          <td><a href="./TableCell.LeftMargin.html" class="internal-link">LeftMargin</a></td>
          <td>左边距</td>
          <td>指定单元格的左文本边距</td>
        </tr>
            <tr>
          <td><a href="./TableCell.Merged.html" class="internal-link">Merged</a></td>
          <td>合并</td>
          <td>确定单元格是否与另一个单元格合并</td>
        </tr>
            <tr>
          <td><a href="./TableCell.RightMargin.html" class="internal-link">RightMargin</a></td>
          <td>右边距</td>
          <td>指定单元格的右侧文本边距</td>
        </tr>
            <tr>
          <td><a href="./TableCell.Row.html" class="internal-link">Row</a></td>
          <td>排</td>
          <td>返回当前单元格所属的行</td>
        </tr>
            <tr>
          <td><a href="./TableCell.RowIndex.html" class="internal-link">RowIndex</a></td>
          <td>行索引</td>
          <td>返回当前单元格所属行的索引</td>
        </tr>
            <tr>
          <td><a href="./TableCell.RowSpan.html" class="internal-link">RowSpan</a></td>
          <td>行跨度</td>
          <td>返回单元格跨越的行数</td>
        </tr>
            <tr>
          <td><a href="./TableCell.Selected.html" class="internal-link">Selected</a></td>
          <td>已选中</td>
          <td>确定单元格是否被选中</td>
        </tr>
            <tr>
          <td><a href="./TableCell.Shapes.html" class="internal-link">Shapes</a></td>
          <td>形状</td>
          <td>返回放置在单元格内的所有形状</td>
        </tr>
            <tr>
          <td><a href="./TableCell.SpanningCell.html" class="internal-link">SpanningCell</a></td>
          <td>生成单元</td>
          <td>获取与当前(不可见)单元格重叠的合并单元格</td>
        </tr>
            <tr>
          <td><a href="./TableCell.TextShape.html" class="internal-link">TextShape</a></td>
          <td>文本形状</td>
          <td>返回单元格文本形状</td>
        </tr>
            <tr>
          <td><a href="./TableCell.TopMargin.html" class="internal-link">TopMargin</a></td>
          <td>上边距</td>
          <td>指定单元格的顶部文本边距</td>
        </tr>
            <tr>
          <td><a href="./TableCell.Visible.html" class="internal-link">Visible</a></td>
          <td>可见的</td>
          <td>确定单元格是否可见(不被合并的单元格重叠)</td>
        </tr>
            <tr>
          <td><a href="./TableCell.Width.html" class="internal-link">Width</a></td>
          <td>宽度</td>
          <td>返回单元格的宽度</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1726" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,15">
<div class="content-fragment-content">


      <div class="content-fragment-header">方法:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./TableCell.AddToSelection.html" class="internal-link">AddToSelection</a></td>
          <td>添加到选择</td>
          <td>将单元格添加到当前选择</td>
        </tr>
            <tr>
          <td><a href="./TableCell.PlaceShape.html" class="internal-link">PlaceShape</a></td>
          <td>地方形状</td>
          <td>在单元格内放置一个形状</td>
        </tr>
            <tr>
          <td><a href="./TableCell.PlaceShapeRange.html" class="internal-link">PlaceShapeRange</a></td>
          <td>放置形状范围</td>
          <td>在单元格内放置形状</td>
        </tr>
            <tr>
          <td><a href="./TableCell.Select.html" class="internal-link">Select</a></td>
          <td>选择</td>
          <td>选择单元格</td>
        </tr>
            <tr>
          <td><a href="./TableCell.SetAllMargins.html" class="internal-link">SetAllMargins</a></td>
          <td>设置所有边距</td>
          <td>指定单元格的文本边距</td>
        </tr>
            <tr>
          <td><a href="./TableCell.SetMargins.html" class="internal-link">SetMargins</a></td>
          <td>设置边距</td>
          <td>指定单元格的文本边距</td>
        </tr>
            <tr>
          <td><a href="./TableCell.Split.html" class="internal-link">Split</a></td>
          <td>分裂</td>
          <td>拆分单元格</td>
        </tr>
            <tr>
          <td><a href="./TableCell.Unmerge.html" class="internal-link">Unmerge</a></td>
          <td>取消合并</td>
          <td>取消合并合并的单元格</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>StructPaletteOptions</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./StructPaletteOptions.html" class="internal-link">StructPaletteOptions</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>StructPaletteOptions class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">StructPaletteOptions 类</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">StructPaletteOptions</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>StructPaletteOptions</b> 类表示将全色图像转换为调色位图时使用的一组导出选项。此类与 <a href="./Document.ExportEx.html">Document.ExportEx</a> 结合使用和 <a href="./Document.ExportBitmap.html">Document.ExportBitmap</a> 方法。</div>
                <div>您可以使用 Visual Basic 中的 <b>New</b> 关键字创建一个新的 <b>StructPaletteOptions</b> 对象。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./StructPaletteOptions.ColorSensitive.html" class="internal-link">ColorSensitive</a></td>
          <td>颜色敏感</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructPaletteOptions.DitherIntensity.html" class="internal-link">DitherIntensity</a></td>
          <td>抖动强度</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructPaletteOptions.DitherType.html" class="internal-link">DitherType</a></td>
          <td>抖动类型</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructPaletteOptions.Importance.html" class="internal-link">Importance</a></td>
          <td>重要性</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructPaletteOptions.Lightness.html" class="internal-link">Lightness</a></td>
          <td>亮度</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructPaletteOptions.NumColors.html" class="internal-link">NumColors</a></td>
          <td>数字颜色</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructPaletteOptions.Palette.html" class="internal-link">Palette</a></td>
          <td>调色板</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructPaletteOptions.PaletteType.html" class="internal-link">PaletteType</a></td>
          <td>调色板类型</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructPaletteOptions.Smoothing.html" class="internal-link">Smoothing</a></td>
          <td>平滑</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructPaletteOptions.TargetColor.html" class="internal-link">TargetColor</a></td>
          <td>目标颜色</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructPaletteOptions.ToleranceA.html" class="internal-link">ToleranceA</a></td>
          <td>公差A</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructPaletteOptions.ToleranceB.html" class="internal-link">ToleranceB</a></td>
          <td>公差B</td>
          <td></td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
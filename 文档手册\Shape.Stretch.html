<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>Shape.Stretch</title> 
                        </head><body>
                        <div><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><a href="./Shape.html" class="internal-link"><strong>Shape</strong></a></div>
<div class="content-fragment-header"><a href="./Shape.Stretch.html" class="internal-link"><strong>Shape.Stretch</strong></a></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">指定一个值以相对于其参考点水平和或垂直拉伸形状</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <div class="content-fragment-header">语法:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
      <div class="code-block"><span class="keyword">Sub</span><span class="space"> </span> <span class="identifier">Stretch</span><span class="punctuation">(</span><span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">StretchX</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Double</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">StretchY</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Double</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="number">0</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">StretchCharactersSize</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Boolean</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="keyword">False</span><span class="punctuation">)</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1721" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,10">
<div class="content-fragment-content">


      <div class="content-fragment-header">参数:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名称</th>
          <th>类型</th>
          <th>描述</th>
        </tr>
            <tr>
          <td>StretchX</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Double</span></div>
                      </td>
          <td><div>指定水平拉伸形状的距离</div></td>
        </tr>
            <tr>
          <td>StretchY</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Double</span></div>
                      </td>
          <td><div>指定垂直拉伸形状的距离。如果省略此值,则假定它与 <b>StretchX</b> 参数值相同,从而产生比例拉伸。</div></td>
        </tr>
            <tr>
          <td>StretchCharactersSize</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Boolean</span></div>
                      </td>
          <td><div>指定字符是否随形状拉伸。</div></td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>Stretch</b> 方法相对于 <a href="./Document.ReferencePoint.html">Document.ReferencePoint</a> 属性指定的参考点拉伸形状。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div id="fragment-1725"></div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1730" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,19">
<div class="content-fragment-content">


      <div class="content-fragment-header">示例:</div>
            <div style="margin-bottom: 1em">下面的 VBA 示例将当前图层上的每个形状相对于每个形状的中心拉伸 <b>200</b>%。</div>
        <div><pre style="code">Sub Test()<br>
 Dim s As Shape<br>
 ActiveDocument.ReferencePoint = cdrCenter<br>
 For Each s In ActiveLayer.Shapes<br>
 s.CreateSelection<br>
 s.Stretch 2<br>
 Next s<br>
End Sub</pre></div>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
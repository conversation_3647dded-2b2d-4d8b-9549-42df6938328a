<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>SelectionInfo</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./SelectionInfo.html" class="internal-link">SelectionInfo</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>SelectionInfo class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">表示选择的设置</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">SelectionInfo</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>SelectionInfo</b> 类表示选择的设置。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.Application.html" class="internal-link">Application</a></td>
          <td>应用</td>
          <td>获取对象所属的应用程序</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.BlendBottomShape.html" class="internal-link">BlendBottomShape</a></td>
          <td>混合底部形状</td>
          <td>获取选定混合的底部形状</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.BlendPath.html" class="internal-link">BlendPath</a></td>
          <td>混合路径</td>
          <td>获取混合路径</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.BlendTopShape.html" class="internal-link">BlendTopShape</a></td>
          <td>混合顶部形状</td>
          <td>获取所选混合的顶部形状</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanApplyBlend.html" class="internal-link">CanApplyBlend</a></td>
          <td>可以应用混合</td>
          <td>获取所选形状是否可以混合</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanApplyContour.html" class="internal-link">CanApplyContour</a></td>
          <td>可以应用轮廓</td>
          <td>获取所选形状是否可以轮廓化</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanApplyDistortion.html" class="internal-link">CanApplyDistortion</a></td>
          <td>可以应用失真</td>
          <td>获取是否可以将变形应用于所选形状</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanApplyEnvelope.html" class="internal-link">CanApplyEnvelope</a></td>
          <td>可以应用信封</td>
          <td>获取是否可以将信封应用于选定的形状</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanApplyFill.html" class="internal-link">CanApplyFill</a></td>
          <td>可以应用填充</td>
          <td>获取所选形状是否可以有填充</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanApplyFillOutline.html" class="internal-link">CanApplyFillOutline</a></td>
          <td>可以应用填充轮廓</td>
          <td>获取是否可以将填充和轮廓应用于所选形状</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanApplyOutline.html" class="internal-link">CanApplyOutline</a></td>
          <td>CanApply轮廓</td>
          <td>获取所选形状是否可以有轮廓</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanApplyTransparency.html" class="internal-link">CanApplyTransparency</a></td>
          <td>可以应用透明度</td>
          <td>获取是否可以将透明度应用于所选形状</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanAssignURL.html" class="internal-link">CanAssignURL</a></td>
          <td>可以分配 URL</td>
          <td>获取是否可以将 URL 分配给选定的形状</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanClone.html" class="internal-link">CanClone</a></td>
          <td>可以克隆</td>
          <td>获取是否可以克隆选定的形状</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanCloneBlend.html" class="internal-link">CanCloneBlend</a></td>
          <td>可以克隆混蛋</td>
          <td>获取是否可以克隆选定的混合</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanCloneContour.html" class="internal-link">CanCloneContour</a></td>
          <td>可以克隆轮廓</td>
          <td>获取所选轮廓是否可以克隆</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanCloneDropShadow.html" class="internal-link">CanCloneDropShadow</a></td>
          <td>可以克隆下降阴影</td>
          <td>获取是否可以复制选定的混合</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanCloneExtrude.html" class="internal-link">CanCloneExtrude</a></td>
          <td>可以克隆挤压</td>
          <td>获取是否可以克隆选定的拉伸</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanCopyBlend.html" class="internal-link">CanCopyBlend</a></td>
          <td>可以复制混合</td>
          <td>获取是否可以复制选定的混合</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanCopyContour.html" class="internal-link">CanCopyContour</a></td>
          <td>可以复制轮廓</td>
          <td>获取所选轮廓是否可以复制</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanCopyDistortion.html" class="internal-link">CanCopyDistortion</a></td>
          <td>可以复制畸变</td>
          <td>获取选中的扭曲是否可以复制</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanCopyDropShadow.html" class="internal-link">CanCopyDropShadow</a></td>
          <td>可以复制下降阴影</td>
          <td>获取是否可以复制选定的阴影</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanCopyEnvelope.html" class="internal-link">CanCopyEnvelope</a></td>
          <td>可以复制信封</td>
          <td>获取选中的信封是否可以复制</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanCopyExtrude.html" class="internal-link">CanCopyExtrude</a></td>
          <td>可以复制挤出</td>
          <td>获取是否可以复制选定的拉伸</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanCopyLens.html" class="internal-link">CanCopyLens</a></td>
          <td>可以复制镜头</td>
          <td>获取选中的镜头是否可以复制</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanCopyPerspective.html" class="internal-link">CanCopyPerspective</a></td>
          <td>可以复制透视</td>
          <td>获取选中的透视图是否可以复制</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanCopyPowerclip.html" class="internal-link">CanCopyPowerclip</a></td>
          <td>可以复制PowerClip</td>
          <td>获取是否可以复制选定的 powerclip</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanCreateBlend.html" class="internal-link">CanCreateBlend</a></td>
          <td>可以创建混合</td>
          <td>获取所选形状是否可以创建混合</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanDeleteControl.html" class="internal-link">CanDeleteControl</a></td>
          <td>可以删除控件</td>
          <td>获取选中的控件是否可以删除</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanLockShapes.html" class="internal-link">CanLockShapes</a></td>
          <td>可以锁定形状</td>
          <td>获取所选形状是否可以锁定</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanPrint.html" class="internal-link">CanPrint</a></td>
          <td>可以打印</td>
          <td>获取当前文档是否可以打印</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanUngroup.html" class="internal-link">CanUngroup</a></td>
          <td>可以取消分组</td>
          <td>获取是否可以将任何选定的形状取消分组</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.CanUnlockShapes.html" class="internal-link">CanUnlockShapes</a></td>
          <td>可以解锁形状</td>
          <td>获取选中的形状是否可以解锁</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.ConnectorLines.html" class="internal-link">ConnectorLines</a></td>
          <td>连接线</td>
          <td>获取选定的连接线</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.ContainsRollOverParent.html" class="internal-link">ContainsRollOverParent</a></td>
          <td>包含RollOverParent</td>
          <td>获取任何选定的形状是否是翻转父级</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.ContourControlShape.html" class="internal-link">ContourControlShape</a></td>
          <td>轮廓控制形状</td>
          <td>获取选定轮廓的控件形状</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.ContourGroup.html" class="internal-link">ContourGroup</a></td>
          <td>轮廓组</td>
          <td>获取选定的轮廓组</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.Count.html" class="internal-link">Count</a></td>
          <td>数数</td>
          <td>获取对象的数量</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.DimensionControlShape.html" class="internal-link">DimensionControlShape</a></td>
          <td>尺寸控制形状</td>
          <td>获取选定维度的控件形状</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.DimensionGroup.html" class="internal-link">DimensionGroup</a></td>
          <td>维度组</td>
          <td>获取选定的维度组</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.DistortionShape.html" class="internal-link">DistortionShape</a></td>
          <td>变形形状</td>
          <td>获取选中形状的扭曲状态</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.DistortionType.html" class="internal-link">DistortionType</a></td>
          <td>失真类型</td>
          <td>获取所选扭曲的类型</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.DropShadowControlShape.html" class="internal-link">DropShadowControlShape</a></td>
          <td>下降阴影控制形状</td>
          <td>获取选定阴影的控制形状</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.DropShadowGroup.html" class="internal-link">DropShadowGroup</a></td>
          <td>下降阴影组</td>
          <td>获取选定的阴影组</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.ExtrudeBevelGroup.html" class="internal-link">ExtrudeBevelGroup</a></td>
          <td>挤出斜面组</td>
          <td>获取选定拉伸的斜角组</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.ExtrudeFaceShape.html" class="internal-link">ExtrudeFaceShape</a></td>
          <td>挤出面形状</td>
          <td>获取选定拉伸的面形状</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.ExtrudeGroup.html" class="internal-link">ExtrudeGroup</a></td>
          <td>挤出组</td>
          <td>获取选定的拉伸组</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.FirstShape.html" class="internal-link">FirstShape</a></td>
          <td>第一形状</td>
          <td>获取第一个选定的形状</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.FirstShapeWithFill.html" class="internal-link">FirstShapeWithFill</a></td>
          <td>填充的第一件形状</td>
          <td>获取第一个带有填充的选定形状</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.FirstShapeWithOutline.html" class="internal-link">FirstShapeWithOutline</a></td>
          <td>第一个带轮廓的形状</td>
          <td>获取第一个带有轮廓的选定形状</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.FittedText.html" class="internal-link">FittedText</a></td>
          <td>适合文本</td>
          <td>获取选定的拟合文本</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.FittedTextControlShape.html" class="internal-link">FittedTextControlShape</a></td>
          <td>拟合文本控制形状</td>
          <td>获取所选拟合文本的控件形状</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.HasAutoLabelText.html" class="internal-link">HasAutoLabelText</a></td>
          <td>有自动标签文本</td>
          <td>获取所选形状是否具有自动标签文本</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsArtisticTextSelected.html" class="internal-link">IsArtisticTextSelected</a></td>
          <td>是否选择了艺术文字</td>
          <td>获取所选形状是否为艺术文字</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsAttachedToDimension.html" class="internal-link">IsAttachedToDimension</a></td>
          <td>附加到尺寸</td>
          <td>获取是否有任何选定的形状附加到维度</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsBevelGroup.html" class="internal-link">IsBevelGroup</a></td>
          <td>是斜角集团</td>
          <td>获取选中的形状是否为斜角组</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsBitmapPresent.html" class="internal-link">IsBitmapPresent</a></td>
          <td>位图是否存在</td>
          <td>获取任何选定的形状是否包含位图</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsBitmapSelected.html" class="internal-link">IsBitmapSelected</a></td>
          <td>是否选择位图</td>
          <td>获取任何选定的形状是否为位图</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsBlendControl.html" class="internal-link">IsBlendControl</a></td>
          <td>是混合控制</td>
          <td>获取所选形状是否为混合控件</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsBlendGroup.html" class="internal-link">IsBlendGroup</a></td>
          <td>是混合组</td>
          <td>获取所选形状是否为混合组</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsCloneControl.html" class="internal-link">IsCloneControl</a></td>
          <td>是克隆控制</td>
          <td>获取选中的形状是否为克隆控件</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsConnector.html" class="internal-link">IsConnector</a></td>
          <td>连接器</td>
          <td>获取选定的形状是否为拟合文本</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsConnectorLine.html" class="internal-link">IsConnectorLine</a></td>
          <td>连接线</td>
          <td>获取所选形状是否为连接线</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsConnectorLineSelected.html" class="internal-link">IsConnectorLineSelected</a></td>
          <td>是否选择了连接线</td>
          <td>获取任何选定的形状是否为连接线</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsConnectorSelected.html" class="internal-link">IsConnectorSelected</a></td>
          <td>是否选择了连接器</td>
          <td>获取任何选定的形状是否为连接线</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsContourControl.html" class="internal-link">IsContourControl</a></td>
          <td>是轮廓控制</td>
          <td>获取选中的形状是否为轮廓控件</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsContourGroup.html" class="internal-link">IsContourGroup</a></td>
          <td>是轮廓组</td>
          <td>获取选中的形状是否为等高线组</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsControlSelected.html" class="internal-link">IsControlSelected</a></td>
          <td>是否选中控件</td>
          <td>获取控件是否被选中</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsControlShape.html" class="internal-link">IsControlShape</a></td>
          <td>是控制形状</td>
          <td>获取选中的形状是否为链接控件</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsDimensionControl.html" class="internal-link">IsDimensionControl</a></td>
          <td>是尺寸控制</td>
          <td>获取选中的形状是否为尺寸控件</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsDistortion.html" class="internal-link">IsDistortion</a></td>
          <td>失真</td>
          <td>获取选中的形状是否为扭曲</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsDistortionPresent.html" class="internal-link">IsDistortionPresent</a></td>
          <td>是否存在失真</td>
          <td>获取任何选定的形状是否是扭曲的</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsDropShadowControl.html" class="internal-link">IsDropShadowControl</a></td>
          <td>是投影控制</td>
          <td>获取所选形状是否为阴影控件</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsDropShadowGroup.html" class="internal-link">IsDropShadowGroup</a></td>
          <td>是投影组</td>
          <td>获取所选形状是否为阴影组</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsEditingRollOver.html" class="internal-link">IsEditingRollOver</a></td>
          <td>正在编辑翻转</td>
          <td>获取翻转编辑状态</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsEditingText.html" class="internal-link">IsEditingText</a></td>
          <td>是编辑文本</td>
          <td>获取文本编辑状态</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsEnvelope.html" class="internal-link">IsEnvelope</a></td>
          <td>是信封</td>
          <td>获取所选形状是否为信封</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsEnvelopePresent.html" class="internal-link">IsEnvelopePresent</a></td>
          <td>信封是否存在</td>
          <td>获取任何选定的形状是否为信封</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsExternalBitmapSelected.html" class="internal-link">IsExternalBitmapSelected</a></td>
          <td>是否选择了外部位图</td>
          <td>获取任何选定的形状是否是外部链接的位图</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsExtrudeControl.html" class="internal-link">IsExtrudeControl</a></td>
          <td>是挤出控制</td>
          <td>获取所选形状是否为拉伸控件</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsExtrudeGroup.html" class="internal-link">IsExtrudeGroup</a></td>
          <td>是挤出组</td>
          <td>获取所选形状是否为拉伸组</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsFittedText.html" class="internal-link">IsFittedText</a></td>
          <td>是适合文本</td>
          <td>获取选定的形状是否为适合的文本控件</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsFittedTextControl.html" class="internal-link">IsFittedTextControl</a></td>
          <td>是否适合文本控件</td>
          <td>获取所选形状是否为自然媒体控件</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsFittedTextSelected.html" class="internal-link">IsFittedTextSelected</a></td>
          <td>是否选择了适合的文本</td>
          <td>获取任何选定的形状是否适合文本</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsGroup.html" class="internal-link">IsGroup</a></td>
          <td>是集团</td>
          <td>获取所选形状是否为组</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsGroupSelected.html" class="internal-link">IsGroupSelected</a></td>
          <td>是否选择组</td>
          <td>获取任何选定的形状是否为组</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsGuidelineSelected.html" class="internal-link">IsGuidelineSelected</a></td>
          <td>是否选择了指南</td>
          <td>获取任何选定的形状是否为准则</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsInternetObjectSelected.html" class="internal-link">IsInternetObjectSelected</a></td>
          <td>是否选择了 Internet 对象</td>
          <td>获取任何选定的形状是否为 Internet 对象</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsLensPresent.html" class="internal-link">IsLensPresent</a></td>
          <td>镜头在场吗</td>
          <td>获取任何选定的形状是否包含透镜</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsLinkControlSelected.html" class="internal-link">IsLinkControlSelected</a></td>
          <td>是否选择了链接控制</td>
          <td>获取任何选定的形状是否是链接控件</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsLinkGroupSelected.html" class="internal-link">IsLinkGroupSelected</a></td>
          <td>是否选择了链接组</td>
          <td>获取任何选定的形状是否是链接组</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsMaskedBitmapPresent.html" class="internal-link">IsMaskedBitmapPresent</a></td>
          <td>是否存在蒙版位图</td>
          <td>获取任何选定的形状是否包含蒙版位图</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsMeshFillPresent.html" class="internal-link">IsMeshFillPresent</a></td>
          <td>是否存在网格填充</td>
          <td>获取任何选定的形状是否为网状填充</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsMeshFillSelected.html" class="internal-link">IsMeshFillSelected</a></td>
          <td>是否选择了网格填充</td>
          <td>获取任何选定的形状是否为网状填充</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsNaturalMediaControl.html" class="internal-link">IsNaturalMediaControl</a></td>
          <td>是自然媒体控制</td>
          <td>获取所选形状是否为自然媒体组</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsNaturalMediaGroup.html" class="internal-link">IsNaturalMediaGroup</a></td>
          <td>是自然传媒集团</td>
          <td>获取所选形状是否为信封</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsNonExternalBitmapSelected.html" class="internal-link">IsNonExternalBitmapSelected</a></td>
          <td>是否选择了非外部位图</td>
          <td>获取任何选定的形状是否为非外部链接位图</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsOLESelected.html" class="internal-link">IsOLESelected</a></td>
          <td>IsOLE已选择</td>
          <td>获取所选形状是否为 OLE 对象</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsOnPowerClipContents.html" class="internal-link">IsOnPowerClipContents</a></td>
          <td>IsOnPowerClip 内容</td>
          <td>获取 powerclip 编辑状态</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsParagraphTextSelected.html" class="internal-link">IsParagraphTextSelected</a></td>
          <td>是否选择了段落文本</td>
          <td>获取所选形状是否为段落文本</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsPerspective.html" class="internal-link">IsPerspective</a></td>
          <td>是透视</td>
          <td>获取所选形状是否为透视图</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsPerspectivePresent.html" class="internal-link">IsPerspectivePresent</a></td>
          <td>是透视现在</td>
          <td>获取任何选定的形状是否是透视图</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsRegularShape.html" class="internal-link">IsRegularShape</a></td>
          <td>是正则形状</td>
          <td>获取选中的形状是否为常规形状</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsRollOverSelected.html" class="internal-link">IsRollOverSelected</a></td>
          <td>是否选择翻转</td>
          <td>获取任何选定的形状是否为翻转</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsSecondContourControl.html" class="internal-link">IsSecondContourControl</a></td>
          <td>是第二个轮廓控制</td>
          <td>获取第二个选中的形状是否为轮廓控件</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsSecondDropShadowControl.html" class="internal-link">IsSecondDropShadowControl</a></td>
          <td>是第二个阴影控制</td>
          <td>获取第二个选定的形状是否为阴影控件</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsSecondExtrudeControl.html" class="internal-link">IsSecondExtrudeControl</a></td>
          <td>是第二个挤出控制</td>
          <td>获取第二个选定的形状是拉伸还是斜角控件</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsSecondNaturalMediaControl.html" class="internal-link">IsSecondNaturalMediaControl</a></td>
          <td>是第二自然媒体控制</td>
          <td>获取第二个选定的形状是否为自然媒体控件</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsSoundObjectSelected.html" class="internal-link">IsSoundObjectSelected</a></td>
          <td>是否选择了声音对象</td>
          <td>获取任何选定的形状是否是声音对象</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsTextSelected.html" class="internal-link">IsTextSelected</a></td>
          <td>是否选择了文本</td>
          <td>获取选中的形状是否为文本</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.IsTextSelection.html" class="internal-link">IsTextSelection</a></td>
          <td>是文本选择</td>
          <td>获取选择是否为文本</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.NaturalMediaControlShape.html" class="internal-link">NaturalMediaControlShape</a></td>
          <td>自然媒体控制形状</td>
          <td>获取自然媒体控件形状</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.NaturalMediaGroup.html" class="internal-link">NaturalMediaGroup</a></td>
          <td>自然媒体集团</td>
          <td>获取自然媒体组</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.Parent.html" class="internal-link">Parent</a></td>
          <td>家长</td>
          <td>获取此对象是其子级的父级</td>
        </tr>
            <tr>
          <td><a href="./SelectionInfo.SecondShape.html" class="internal-link">SecondShape</a></td>
          <td>第二形状</td>
          <td>获取第二个选定的形状</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>Shape.Effect</title> 
                        </head><body>
                        <div><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><a href="./Shape.html" class="internal-link"><strong>Shape</strong></a></div>
<div class="content-fragment-header"><a href="./Shape.Effect.html" class="internal-link"><strong>Shape.Effect</strong></a></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">如果适用,返回当前对象的效果对象</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <div class="content-fragment-header">语法:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
      <div class="code-block"><span class="keyword">Property</span><span class="space"> </span> <span class="keyword">Get</span><span class="space"> </span> <span class="identifier">Effect</span><span class="punctuation">(</span><span class="punctuation">)</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./Effect.html" class="internal-link">Effect</a></span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>Effect</b> 属性表示由效果(轮廓或混合组、挤压面等)生成的形状的 <a href="./Effect.html">Effect</a> 对象。</div>
                <div><b>Effect</b> 属性表示只读值。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div id="fragment-1725"></div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1730" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,19">
<div class="content-fragment-content">


      <div class="content-fragment-header">示例:</div>
            <div style="margin-bottom: 1em">以下 VBA 示例在两个椭圆之间创建混合。它遍历页面上的所有形状以定位混合组并更改一些混合参数。此示例只是 <b>Effect</b> 属性的演示；如果要设置创建的混合的属性，可以直接使用 <a href="./Shape.CreateBlend.html">CreateBlend</a> 方法返回的 <b>Effect</b> 对象。</div>
        <div><pre style="code">Sub Test()<br>
 Dim doc As Document<br>
 Dim e1 As Shape, e2 As Shape, s As Shape<br>
 Set doc = CreateDocument<br>
 Set e1 = doc.ActiveLayer.CreateEllipse(0, 0, 2, 2)<br>
 e1.Fill.UniformColor.RGBAssign 255, 0, 0<br>
 Set e2 = doc.ActiveLayer.CreateEllipse(4, 4, 5, 5)<br>
 e2.Fill.UniformColor.RGBAssign 0, 0, 255<br>
 e1.CreateBlend e2<br>
 For Each s In doc.ActivePage.Shapes<br>
 If s.Type = cdrBlendGroupShape Then<br>
 With s.Effect.Blend<br>
 .Steps = 5<br>
 .ColorBlendType = cdrRainbowCWFountainFillBlend<br>
 End With<br>
 End If<br>
 Next s<br>
End Sub</pre></div>
            <div style="margin-bottom: 1em">以下 VBA 示例在创建混合后立即更改混合效果属性。或者,您可以直接在 <a href="./Shape.CreateBlend.html">CreateBlend</a> 方法中设置大部分混合参数。</div>
        <div><pre style="code">Sub Test()<br>
 Dim doc As Document<br>
 Dim e1 As Shape, e2 As Shape, eff As Effect<br>
 Set doc = CreateDocument<br>
 Set e1 = doc.ActiveLayer.CreateEllipse(0, 0, 2, 2)<br>
 e1.Fill.UniformColor.RGBAssign 255, 0, 0<br>
 Set e2 = doc.ActiveLayer.CreateEllipse(4, 4, 5, 5)<br>
 e2.Fill.UniformColor.RGBAssign 0, 0, 255<br>
 Set eff = e1.CreateBlend(e2)<br>
 eff.Blend.Steps = 5<br>
 eff.Blend.ColorBlendType = cdrRainbowCWFountainFillBlend<br>
End Sub</pre></div>
            <div style="margin-bottom: 1em">以下 VBA 示例运行效率更高,在单个命令中设置所有混合参数。</div>
        <div><pre style="code">Sub Test()<br>
 Dim doc As Document<br>
 Dim e1 As Shape, e2 As Shape, eff As Effect<br>
 Set doc = CreateDocument<br>
 Set e1 = doc.ActiveLayer.CreateEllipse(0, 0, 2, 2)<br>
 e1.Fill.UniformColor.RGBAssign 255, 0, 0<br>
 Set e2 = doc.ActiveLayer.CreateEllipse(4, 4, 5, 5)<br>
 e2.Fill.UniformColor.RGBAssign 0, 0, 255<br>
 e1.CreateBlend e2, 5, cdrRainbowCWFountainFillBlend<br>
End Sub</pre></div>
            <div style="margin-bottom: 1em">以下 VBA 示例更改页面上找到的所有混合中的步骤数并删除所有拉伸。</div>
        <div><pre style="code">Sub Test()<br>
 Dim s As Shape<br>
 For Each s In ActivePage.Shapes<br>
 Select Case s.Type<br>
 Case cdrExtrudeGroupShape<br>
 s.Effect.Clear<br>
 Case cdrBlendGroupShape<br>
 s.Effect.Blend.Steps = 50<br>
 End Select<br>
 Next s<br>
End Sub</pre></div>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>Shape</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./Shape.html" class="internal-link">Shape</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>Shape class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">表示 CorelDRAW 或 Corel DESIGNER 形状对象(或“形状”)、组或选择的设置;并允许编辑现有形状</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">Shape</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>Shape</b> 类表示 CorelDRAW 形状对象(或“形状”)、组或选择的设置。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./Shape.AbsoluteHScale.html" class="internal-link">AbsoluteHScale</a></td>
          <td>绝对HScale</td>
          <td>返回绝对水平比例。这是应用于形状的所有水平拉伸的总和。</td>
        </tr>
            <tr>
          <td><a href="./Shape.AbsoluteSkew.html" class="internal-link">AbsoluteSkew</a></td>
          <td>绝对偏斜</td>
          <td>返回自创建以来应用于形状的绝对倾斜角度</td>
        </tr>
            <tr>
          <td><a href="./Shape.AbsoluteVScale.html" class="internal-link">AbsoluteVScale</a></td>
          <td>绝对VScale</td>
          <td>返回绝对垂直比例。这是应用于形状的所有垂直拉伸的总和。</td>
        </tr>
            <tr>
          <td><a href="./Shape.Application.html" class="internal-link">Application</a></td>
          <td>应用</td>
          <td>返回对象所属的应用程序</td>
        </tr>
            <tr>
          <td><a href="./Shape.Bitmap.html" class="internal-link">Bitmap</a></td>
          <td>位图</td>
          <td>允许用户访问位图属性</td>
        </tr>
            <tr>
          <td><a href="./Shape.BottomY.html" class="internal-link">BottomY</a></td>
          <td>底部Y</td>
          <td>返回或设置形状下边缘的 Y 坐标</td>
        </tr>
            <tr>
          <td><a href="./Shape.BoundingBox.html" class="internal-link">BoundingBox</a></td>
          <td>边界框</td>
          <td>返回形状周围的边界矩形</td>
        </tr>
            <tr>
          <td><a href="./Shape.BSpline.html" class="internal-link">BSpline</a></td>
          <td>B样条</td>
          <td>返回一个只读的 BSpline 对象</td>
        </tr>
            <tr>
          <td><a href="./Shape.CanHaveFill.html" class="internal-link">CanHaveFill</a></td>
          <td>可以有填充</td>
          <td>如果可以将填充应用于给定的形状,则返回 True</td>
        </tr>
            <tr>
          <td><a href="./Shape.CanHaveOutline.html" class="internal-link">CanHaveOutline</a></td>
          <td>可以有轮廓</td>
          <td>如果轮廓可以应用于给定的形状,则返回 True</td>
        </tr>
            <tr>
          <td><a href="./Shape.CenterX.html" class="internal-link">CenterX</a></td>
          <td>中心X</td>
          <td>返回或设置形状中心的水平位置</td>
        </tr>
            <tr>
          <td><a href="./Shape.CenterY.html" class="internal-link">CenterY</a></td>
          <td>中心Y</td>
          <td>返回或设置形状中心的垂直位置</td>
        </tr>
            <tr>
          <td><a href="./Shape.CloneLink.html" class="internal-link">CloneLink</a></td>
          <td>克隆链接</td>
          <td>返回克隆链接属性</td>
        </tr>
            <tr>
          <td><a href="./Shape.Clones.html" class="internal-link">Clones</a></td>
          <td>克隆</td>
          <td>返回克隆形状的集合</td>
        </tr>
            <tr>
          <td><a href="./Shape.Connector.html" class="internal-link">Connector</a></td>
          <td>连接器</td>
          <td>允许用户访问连接器属性</td>
        </tr>
            <tr>
          <td><a href="./Shape.Curve.html" class="internal-link">Curve</a></td>
          <td>曲线</td>
          <td>允许用户访问曲线属性</td>
        </tr>
            <tr>
          <td><a href="./Shape.Custom.html" class="internal-link">Custom</a></td>
          <td>风俗</td>
          <td>返回自定义形状的属性</td>
        </tr>
            <tr>
          <td><a href="./Shape.Dimension.html" class="internal-link">Dimension</a></td>
          <td>方面</td>
          <td>返回一个只读的 Dimension 对象</td>
        </tr>
            <tr>
          <td><a href="./Shape.DisplayCurve.html" class="internal-link">DisplayCurve</a></td>
          <td>显示曲线</td>
          <td>返回表示当前形状的只读曲线对象</td>
        </tr>
            <tr>
          <td><a href="./Shape.DrapeFill.html" class="internal-link">DrapeFill</a></td>
          <td>悬垂填充</td>
          <td>返回或设置是否有 DrapeFill</td>
        </tr>
            <tr>
          <td><a href="./Shape.Effect.html" class="internal-link">Effect</a></td>
          <td>影响</td>
          <td>如果适用,返回当前对象的效果对象</td>
        </tr>
            <tr>
          <td><a href="./Shape.Effects.html" class="internal-link">Effects</a></td>
          <td>效果</td>
          <td>返回应用于对象的效果集合</td>
        </tr>
            <tr>
          <td><a href="./Shape.Ellipse.html" class="internal-link">Ellipse</a></td>
          <td>椭圆</td>
          <td>允许用户访问椭圆属性</td>
        </tr>
            <tr>
          <td><a href="./Shape.EPS.html" class="internal-link">EPS</a></td>
          <td>每股收益</td>
          <td>返回 Embedded PostScript 对象的属性</td>
        </tr>
            <tr>
          <td><a href="./Shape.Fill.html" class="internal-link">Fill</a></td>
          <td>充满</td>
          <td>返回或设置填充属性</td>
        </tr>
            <tr>
          <td><a href="./Shape.FillMode.html" class="internal-link">FillMode</a></td>
          <td>填充模式</td>
          <td>返回或设置形状的填充模式</td>
        </tr>
            <tr>
          <td><a href="./Shape.Guide.html" class="internal-link">Guide</a></td>
          <td>指导</td>
          <td>允许用户访问指南属性</td>
        </tr>
            <tr>
          <td><a href="./Shape.IsSimpleShape.html" class="internal-link">IsSimpleShape</a></td>
          <td>是简单形状</td>
          <td>如果形状不是任何类型的组形状,则返回 True</td>
        </tr>
            <tr>
          <td><a href="./Shape.Layer.html" class="internal-link">Layer</a></td>
          <td>层</td>
          <td>返回或设置形状所在的图层</td>
        </tr>
            <tr>
          <td><a href="./Shape.LeftX.html" class="internal-link">LeftX</a></td>
          <td>左X</td>
          <td>返回或设置形状左边缘的 X 坐标</td>
        </tr>
            <tr>
          <td><a href="./Shape.Locked.html" class="internal-link">Locked</a></td>
          <td>锁定</td>
          <td>返回或设置形状是否被锁定</td>
        </tr>
            <tr>
          <td><a href="./Shape.Name.html" class="internal-link">Name</a></td>
          <td>名称</td>
          <td>返回或设置形状的名称</td>
        </tr>
            <tr>
          <td><a href="./Shape.Next.html" class="internal-link">Next</a></td>
          <td>下一个</td>
          <td>返回下一个形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.ObjectData.html" class="internal-link">ObjectData</a></td>
          <td>对象数据</td>
          <td>返回一个 DataItems 集合,它表示与形状关联的对象数据</td>
        </tr>
            <tr>
          <td><a href="./Shape.OLE.html" class="internal-link">OLE</a></td>
          <td>奥莱</td>
          <td>返回一个 OLE 对象</td>
        </tr>
            <tr>
          <td><a href="./Shape.OriginalHeight.html" class="internal-link">OriginalHeight</a></td>
          <td>原始高度</td>
          <td>返回形状的原始高度,忽略创建后可能已应用的任何转换。</td>
        </tr>
            <tr>
          <td><a href="./Shape.OriginalWidth.html" class="internal-link">OriginalWidth</a></td>
          <td>原始宽度</td>
          <td>返回形状的原始宽度,忽略创建后可能已应用的任何转换。</td>
        </tr>
            <tr>
          <td><a href="./Shape.Outline.html" class="internal-link">Outline</a></td>
          <td>轮廓</td>
          <td>允许用户访问轮廓属性</td>
        </tr>
            <tr>
          <td><a href="./Shape.OverprintBitmap.html" class="internal-link">OverprintBitmap</a></td>
          <td>叠印位图</td>
          <td>返回或设置是否应叠印位图</td>
        </tr>
            <tr>
          <td><a href="./Shape.OverprintFill.html" class="internal-link">OverprintFill</a></td>
          <td>叠印填充</td>
          <td>返回或设置是否应叠印填充</td>
        </tr>
            <tr>
          <td><a href="./Shape.OverprintOutline.html" class="internal-link">OverprintOutline</a></td>
          <td>套印轮廓</td>
          <td>返回或设置轮廓是否应叠印</td>
        </tr>
            <tr>
          <td><a href="./Shape.Page.html" class="internal-link">Page</a></td>
          <td>页</td>
          <td>返回形状所属的页面</td>
        </tr>
            <tr>
          <td><a href="./Shape.Parent.html" class="internal-link">Parent</a></td>
          <td>家长</td>
          <td>返回对象是子对象的父对象:如果形状是选择形状,它将是一个文档,否则它将是一个图层</td>
        </tr>
            <tr>
          <td><a href="./Shape.ParentGroup.html" class="internal-link">ParentGroup</a></td>
          <td>父组</td>
          <td>返回一个形状对象,该对象表示活动形状所属的组形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.PixelAlignedRendering.html" class="internal-link">PixelAlignedRendering</a></td>
          <td>像素对齐渲染</td>
          <td>返回并设置对齐到形状的像素属性</td>
        </tr>
            <tr>
          <td><a href="./Shape.Polygon.html" class="internal-link">Polygon</a></td>
          <td>多边形</td>
          <td>允许用户访问多边形属性</td>
        </tr>
            <tr>
          <td><a href="./Shape.PositionX.html" class="internal-link">PositionX</a></td>
          <td>位置X</td>
          <td>根据文档的参考点返回或设置形状在页面上的水平位置 (X)</td>
        </tr>
            <tr>
          <td><a href="./Shape.PositionY.html" class="internal-link">PositionY</a></td>
          <td>位置Y</td>
          <td>根据文档的参考点返回或设置形状在页面上的垂直位置 (Y)</td>
        </tr>
            <tr>
          <td><a href="./Shape.PowerClip.html" class="internal-link">PowerClip</a></td>
          <td>强力剪辑</td>
          <td>返回 PowerClip</td>
        </tr>
            <tr>
          <td><a href="./Shape.PowerClipParent.html" class="internal-link">PowerClipParent</a></td>
          <td>PowerClip父级</td>
          <td>返回 PowerClip 父级</td>
        </tr>
            <tr>
          <td><a href="./Shape.Previous.html" class="internal-link">Previous</a></td>
          <td>以前的</td>
          <td>返回上一个形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.Properties.html" class="internal-link">Properties</a></td>
          <td>特性</td>
          <td>允许访问形状的个人属性</td>
        </tr>
            <tr>
          <td><a href="./Shape.Rectangle.html" class="internal-link">Rectangle</a></td>
          <td>长方形</td>
          <td>允许用户访问矩形属性</td>
        </tr>
            <tr>
          <td><a href="./Shape.RightX.html" class="internal-link">RightX</a></td>
          <td>右X</td>
          <td>返回或设置形状右边缘的 X 坐标</td>
        </tr>
            <tr>
          <td><a href="./Shape.RotationAngle.html" class="internal-link">RotationAngle</a></td>
          <td>旋转角度</td>
          <td>返回或设置页面上形??状的旋转角度</td>
        </tr>
            <tr>
          <td><a href="./Shape.RotationCenterX.html" class="internal-link">RotationCenterX</a></td>
          <td>旋转中心X</td>
          <td>返回或设置页面上形??状的水平旋转中心 (X)</td>
        </tr>
            <tr>
          <td><a href="./Shape.RotationCenterY.html" class="internal-link">RotationCenterY</a></td>
          <td>旋转中心Y</td>
          <td>返回或设置页面上形??状的垂直旋转中心 (Y)</td>
        </tr>
            <tr>
          <td><a href="./Shape.Selectable.html" class="internal-link">Selectable</a></td>
          <td>可选择的</td>
          <td>确定形状当前是否可见且未锁定</td>
        </tr>
            <tr>
          <td><a href="./Shape.Selected.html" class="internal-link">Selected</a></td>
          <td>已选中</td>
          <td>返回或设置对象的选择状态;如果选择,则单选</td>
        </tr>
            <tr>
          <td><a href="./Shape.Shapes.html" class="internal-link">Shapes</a></td>
          <td>形状</td>
          <td>返回形状的集合</td>
        </tr>
            <tr>
          <td><a href="./Shape.SizeHeight.html" class="internal-link">SizeHeight</a></td>
          <td>尺寸高度</td>
          <td>返回或设置页面形状的高度</td>
        </tr>
            <tr>
          <td><a href="./Shape.SizeWidth.html" class="internal-link">SizeWidth</a></td>
          <td>尺寸宽度</td>
          <td>返回或设置页面上形??状的宽度</td>
        </tr>
            <tr>
          <td><a href="./Shape.SnapPoints.html" class="internal-link">SnapPoints</a></td>
          <td>快照点</td>
          <td>返回对象的积分集合</td>
        </tr>
            <tr>
          <td><a href="./Shape.Spread.html" class="internal-link">Spread</a></td>
          <td>传播</td>
          <td>返回形状所属的页面展开</td>
        </tr>
            <tr>
          <td><a href="./Shape.StaticID.html" class="internal-link">StaticID</a></td>
          <td>静态ID</td>
          <td>返回对象数据管理器的静态 ID 字段</td>
        </tr>
            <tr>
          <td><a href="./Shape.Style.html" class="internal-link">Style</a></td>
          <td>风格</td>
          <td>返回应用于形状的样式集</td>
        </tr>
            <tr>
          <td><a href="./Shape.Symbol.html" class="internal-link">Symbol</a></td>
          <td>象征</td>
          <td>返回形状的 Symbol 属性</td>
        </tr>
            <tr>
          <td><a href="./Shape.Text.html" class="internal-link">Text</a></td>
          <td>文本</td>
          <td>允许用户访问文本属性</td>
        </tr>
            <tr>
          <td><a href="./Shape.TextWrapOffset.html" class="internal-link">TextWrapOffset</a></td>
          <td>文字环绕偏移</td>
          <td>返回或设置文本换行偏移量</td>
        </tr>
            <tr>
          <td><a href="./Shape.TopY.html" class="internal-link">TopY</a></td>
          <td>顶部 Y</td>
          <td>返回或设置形状上边缘的 Y 坐标</td>
        </tr>
            <tr>
          <td><a href="./Shape.Transparency.html" class="internal-link">Transparency</a></td>
          <td>透明度</td>
          <td>如果适用,返回当前对象的透明度</td>
        </tr>
            <tr>
          <td><a href="./Shape.TreeNode.html" class="internal-link">TreeNode</a></td>
          <td>树节点</td>
          <td>返回与给定形状对象对应的树节点</td>
        </tr>
            <tr>
          <td><a href="./Shape.Type.html" class="internal-link">Type</a></td>
          <td>类型</td>
          <td>返回形状的类型以区分形状类型(矩形、椭圆、文本、曲线等)</td>
        </tr>
            <tr>
          <td><a href="./Shape.URL.html" class="internal-link">URL</a></td>
          <td>网址</td>
          <td>返回形状的超链接</td>
        </tr>
            <tr>
          <td><a href="./Shape.Virtual.html" class="internal-link">Virtual</a></td>
          <td>虚拟的</td>
          <td>如果形状是临时对象,则返回 True</td>
        </tr>
            <tr>
          <td><a href="./Shape.WrapText.html" class="internal-link">WrapText</a></td>
          <td>包装文本</td>
          <td>返回或设置是否覆盖文本将环绕形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.ZOrder.html" class="internal-link">ZOrder</a></td>
          <td>Z顺序</td>
          <td>返回形状在其父形状集合中的索引</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1726" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,15">
<div class="content-fragment-content">


      <div class="content-fragment-header">方法:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./Shape.AddToPowerClip.html" class="internal-link">AddToPowerClip</a></td>
          <td>添加到PowerClip</td>
          <td>将此形状添加到 PowerClip 对象</td>
        </tr>
            <tr>
          <td><a href="./Shape.AddToSelection.html" class="internal-link">AddToSelection</a></td>
          <td>添加到选择</td>
          <td>将形状添加到选择中;如果形状是选择形状则不起作用</td>
        </tr>
            <tr>
          <td><a href="./Shape.AffineTransform.html" class="internal-link">AffineTransform</a></td>
          <td>仿射变换</td>
          <td>相对于变换的中心点对形状应用仿射变换(由变换矩阵组件定义)</td>
        </tr>
            <tr>
          <td><a href="./Shape.AlignAndDistribute.html" class="internal-link">AlignAndDistribute</a></td>
          <td>对齐和分布</td>
          <td>对齐和/或分布选择中的一个或多个形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.AlignToGrid.html" class="internal-link">AlignToGrid</a></td>
          <td>对齐网格</td>
          <td>将形状与网格对齐</td>
        </tr>
            <tr>
          <td><a href="./Shape.AlignToPage.html" class="internal-link">AlignToPage</a></td>
          <td>对齐到页面</td>
          <td>将形状与页面对齐</td>
        </tr>
            <tr>
          <td><a href="./Shape.AlignToPageCenter.html" class="internal-link">AlignToPageCenter</a></td>
          <td>对齐到页面中心</td>
          <td>将形状与页面中心对齐</td>
        </tr>
            <tr>
          <td><a href="./Shape.AlignToPoint.html" class="internal-link">AlignToPoint</a></td>
          <td>对齐到点</td>
          <td>将形状与指定的坐标对齐</td>
        </tr>
            <tr>
          <td><a href="./Shape.AlignToShape.html" class="internal-link">AlignToShape</a></td>
          <td>对齐形状</td>
          <td>将一个形状与另一个形状对齐</td>
        </tr>
            <tr>
          <td><a href="./Shape.AlignToShapeRange.html" class="internal-link">AlignToShapeRange</a></td>
          <td>对齐形状范围</td>
          <td>将形状与 ShapeRange 对齐</td>
        </tr>
            <tr>
          <td><a href="./Shape.ApplyEffectBCI.html" class="internal-link">ApplyEffectBCI</a></td>
          <td>应用效果BCI</td>
          <td>应用亮度-对比度-强度颜色效果</td>
        </tr>
            <tr>
          <td><a href="./Shape.ApplyEffectColorBalance.html" class="internal-link">ApplyEffectColorBalance</a></td>
          <td>应用效果颜色平衡</td>
          <td>应用色彩平衡效果</td>
        </tr>
            <tr>
          <td><a href="./Shape.ApplyEffectGamma.html" class="internal-link">ApplyEffectGamma</a></td>
          <td>申请效果伽玛</td>
          <td>应用伽马颜色校正效果</td>
        </tr>
            <tr>
          <td><a href="./Shape.ApplyEffectHSL.html" class="internal-link">ApplyEffectHSL</a></td>
          <td>应用效果HSL</td>
          <td>应用色相-饱和度-亮度颜色效果</td>
        </tr>
            <tr>
          <td><a href="./Shape.ApplyEffectInvert.html" class="internal-link">ApplyEffectInvert</a></td>
          <td>申请效果反转</td>
          <td>反转形状或选择的颜色</td>
        </tr>
            <tr>
          <td><a href="./Shape.ApplyEffectPosterize.html" class="internal-link">ApplyEffectPosterize</a></td>
          <td>申请效果海报</td>
          <td>应用色调分离颜色效果</td>
        </tr>
            <tr>
          <td><a href="./Shape.ApplyStyle.html" class="internal-link">ApplyStyle</a></td>
          <td>应用样式</td>
          <td>应用文本/图形样式</td>
        </tr>
            <tr>
          <td><a href="./Shape.BreakApart.html" class="internal-link">BreakApart</a></td>
          <td>分手</td>
          <td>拆分之前与另一个形状组合的形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.BreakApartEx.html" class="internal-link">BreakApartEx</a></td>
          <td>分解前</td>
          <td>将曲线分开并将对象作为形状范围返回</td>
        </tr>
            <tr>
          <td><a href="./Shape.Chamfer.html" class="internal-link">Chamfer</a></td>
          <td>倒角</td>
          <td>在形状的每个尖角上创建倒角</td>
        </tr>
            <tr>
          <td><a href="./Shape.ClearEffect.html" class="internal-link">ClearEffect</a></td>
          <td>清除效果</td>
          <td>清除应用于形状的任何效果</td>
        </tr>
            <tr>
          <td><a href="./Shape.ClearTransformations.html" class="internal-link">ClearTransformations</a></td>
          <td>清除转换</td>
          <td>清除应用于形状的所有旋转以及拉伸和倾斜变换</td>
        </tr>
            <tr>
          <td><a href="./Shape.Clone.html" class="internal-link">Clone</a></td>
          <td>克隆</td>
          <td>克隆形状并返回对克隆形状的引用</td>
        </tr>
            <tr>
          <td><a href="./Shape.CloneAsRange.html" class="internal-link">CloneAsRange</a></td>
          <td>克隆范围</td>
          <td>克隆形状或选择并将克隆的形状作为形状范围返回</td>
        </tr>
            <tr>
          <td><a href="./Shape.Combine.html" class="internal-link">Combine</a></td>
          <td>结合</td>
          <td>将一个形状与另一个形状组合在一起。形状必须是同一选择的一部分。</td>
        </tr>
            <tr>
          <td><a href="./Shape.CompareTo.html" class="internal-link">CompareTo</a></td>
          <td>相比于</td>
          <td>比较两个形状的属性</td>
        </tr>
            <tr>
          <td><a href="./Shape.CompareToEx.html" class="internal-link">CompareToEx</a></td>
          <td>与前任相比</td>
          <td>使用 CQL 表达式作为条件比较两个形状,如果满足条件,则返回 True</td>
        </tr>
            <tr>
          <td><a href="./Shape.ConvertToBitmap.html" class="internal-link">ConvertToBitmap</a></td>
          <td>转换为位图</td>
          <td>将形状从矢量栅格化为位图对象</td>
        </tr>
            <tr>
          <td><a href="./Shape.ConvertToBitmapEx.html" class="internal-link">ConvertToBitmapEx</a></td>
          <td>转换为位图</td>
          <td>将形状转换为位图</td>
        </tr>
            <tr>
          <td><a href="./Shape.ConvertToCurves.html" class="internal-link">ConvertToCurves</a></td>
          <td>转换为曲线</td>
          <td>将形状转换为曲线</td>
        </tr>
            <tr>
          <td><a href="./Shape.ConvertToSymbol.html" class="internal-link">ConvertToSymbol</a></td>
          <td>转换为符号</td>
          <td>将形状转换为符号</td>
        </tr>
            <tr>
          <td><a href="./Shape.Copy.html" class="internal-link">Copy</a></td>
          <td>复制</td>
          <td>将形状复制到剪贴板</td>
        </tr>
            <tr>
          <td><a href="./Shape.CopyPropertiesFrom.html" class="internal-link">CopyPropertiesFrom</a></td>
          <td>复制属性</td>
          <td>从另一个形状复制填充、轮廓和/或文本属性</td>
        </tr>
            <tr>
          <td><a href="./Shape.CopyToLayer.html" class="internal-link">CopyToLayer</a></td>
          <td>复制到图层</td>
          <td>将形状复制到指定图层</td>
        </tr>
            <tr>
          <td><a href="./Shape.CopyToLayerAsRange.html" class="internal-link">CopyToLayerAsRange</a></td>
          <td>复制到图层为范围</td>
          <td>将形状或选区复制到指定图层并将复制的形状作为范围返回</td>
        </tr>
            <tr>
          <td><a href="./Shape.CreateArrowHead.html" class="internal-link">CreateArrowHead</a></td>
          <td>创建箭头头</td>
          <td>从形状创建箭头</td>
        </tr>
            <tr>
          <td><a href="./Shape.CreateBlend.html" class="internal-link">CreateBlend</a></td>
          <td>创建混合</td>
          <td>混合两种形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.CreateBoundary.html" class="internal-link">CreateBoundary</a></td>
          <td>创建边界</td>
          <td>在 Shape 对象周围创建一个边界形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.CreateContour.html" class="internal-link">CreateContour</a></td>
          <td>创建轮廓</td>
          <td>创建轮廓效果</td>
        </tr>
            <tr>
          <td><a href="./Shape.CreateCustomDistortion.html" class="internal-link">CreateCustomDistortion</a></td>
          <td>创建自定义失真</td>
          <td>将自定义扭曲应用于形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.CreateCustomEffect.html" class="internal-link">CreateCustomEffect</a></td>
          <td>创建自定义效果</td>
          <td>将自定义效果应用于形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.CreateDocumentFrom.html" class="internal-link">CreateDocumentFrom</a></td>
          <td>创建文档自</td>
          <td>创建包含形状的文档的副本</td>
        </tr>
            <tr>
          <td><a href="./Shape.CreateDropShadow.html" class="internal-link">CreateDropShadow</a></td>
          <td>创建DropShadow</td>
          <td>对形状应用阴影效果</td>
        </tr>
            <tr>
          <td><a href="./Shape.CreateEnvelope.html" class="internal-link">CreateEnvelope</a></td>
          <td>创建信封</td>
          <td>创建包络效果</td>
        </tr>
            <tr>
          <td><a href="./Shape.CreateEnvelopeFromCurve.html" class="internal-link">CreateEnvelopeFromCurve</a></td>
          <td>从曲线创建包络</td>
          <td>使用指定曲线作为模板创建包络效果</td>
        </tr>
            <tr>
          <td><a href="./Shape.CreateEnvelopeFromShape.html" class="internal-link">CreateEnvelopeFromShape</a></td>
          <td>从形状创造信封</td>
          <td>使用指定的形状作为模板创建包络效果</td>
        </tr>
            <tr>
          <td><a href="./Shape.CreateExtrude.html" class="internal-link">CreateExtrude</a></td>
          <td>创建拉伸</td>
          <td>创建拉伸效果</td>
        </tr>
            <tr>
          <td><a href="./Shape.CreateLens.html" class="internal-link">CreateLens</a></td>
          <td>创造镜头</td>
          <td>创建镜头效果</td>
        </tr>
            <tr>
          <td><a href="./Shape.CreatePerspective.html" class="internal-link">CreatePerspective</a></td>
          <td>创建透视</td>
          <td>创建透视效果</td>
        </tr>
            <tr>
          <td><a href="./Shape.CreatePushPullDistortion.html" class="internal-link">CreatePushPullDistortion</a></td>
          <td>创建推拉失真</td>
          <td>创建推挽失真效果</td>
        </tr>
            <tr>
          <td><a href="./Shape.CreateSelection.html" class="internal-link">CreateSelection</a></td>
          <td>创建选择</td>
          <td>从形状创建选择</td>
        </tr>
            <tr>
          <td><a href="./Shape.CreateTwisterDistortion.html" class="internal-link">CreateTwisterDistortion</a></td>
          <td>创造扭转失真</td>
          <td>创建扭曲变形效果</td>
        </tr>
            <tr>
          <td><a href="./Shape.CreateZipperDistortion.html" class="internal-link">CreateZipperDistortion</a></td>
          <td>创建拉链失真</td>
          <td>创建拉链变形效果</td>
        </tr>
            <tr>
          <td><a href="./Shape.CustomCommand.html" class="internal-link">CustomCommand</a></td>
          <td>自定义命令</td>
          <td>在给定形状上执行自定义命令</td>
        </tr>
            <tr>
          <td><a href="./Shape.Cut.html" class="internal-link">Cut</a></td>
          <td>切</td>
          <td>从页面中删除形状,并将其复制到剪贴板</td>
        </tr>
            <tr>
          <td><a href="./Shape.Delete.html" class="internal-link">Delete</a></td>
          <td>删除</td>
          <td>删除形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.Distribute.html" class="internal-link">Distribute</a></td>
          <td>分发</td>
          <td>在选择中均匀分布形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.Duplicate.html" class="internal-link">Duplicate</a></td>
          <td>复制</td>
          <td>复制形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.DuplicateAsRange.html" class="internal-link">DuplicateAsRange</a></td>
          <td>复制范围</td>
          <td>复制形状或选择并将复制的形状作为形状范围返回</td>
        </tr>
            <tr>
          <td><a href="./Shape.EqualDivide.html" class="internal-link">EqualDivide</a></td>
          <td>等分</td>
          <td>将对象分成相等的部分</td>
        </tr>
            <tr>
          <td><a href="./Shape.Evaluate.html" class="internal-link">Evaluate</a></td>
          <td>评价</td>
          <td>使用当前形状的属性评估给定的 CQL 表达式并返回表达式的结果</td>
        </tr>
            <tr>
          <td><a href="./Shape.Fillet.html" class="internal-link">Fillet</a></td>
          <td>鱼片</td>
          <td>在形状的每个尖角上创建圆角</td>
        </tr>
            <tr>
          <td><a href="./Shape.FindSnapPoint.html" class="internal-link">FindSnapPoint</a></td>
          <td>查找捕捉点</td>
          <td>使用参考数据查找捕捉点</td>
        </tr>
            <tr>
          <td><a href="./Shape.Flip.html" class="internal-link">Flip</a></td>
          <td>翻动</td>
          <td>水平和或垂直镜像现有形状。没有创建新对象。</td>
        </tr>
            <tr>
          <td><a href="./Shape.GetBoundingBox.html" class="internal-link">GetBoundingBox</a></td>
          <td>获取边界框</td>
          <td>返回相对于其左下角的形状边界框</td>
        </tr>
            <tr>
          <td><a href="./Shape.GetLinkedShapes.html" class="internal-link">GetLinkedShapes</a></td>
          <td>获取LinkedShapes</td>
          <td>返回以某种方式链接到当前形状的所有形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.GetMatrix.html" class="internal-link">GetMatrix</a></td>
          <td>获取矩阵</td>
          <td>返回变换形状的变换矩阵中使用的值</td>
        </tr>
            <tr>
          <td><a href="./Shape.GetOverprintFillState.html" class="internal-link">GetOverprintFillState</a></td>
          <td>获取叠印填充状态</td>
          <td>返回形状的叠印填充状态标志</td>
        </tr>
            <tr>
          <td><a href="./Shape.GetOverprintOutlineState.html" class="internal-link">GetOverprintOutlineState</a></td>
          <td>获取套印轮廓状态</td>
          <td>返回形状的叠印轮廓状态标志</td>
        </tr>
            <tr>
          <td><a href="./Shape.GetPosition.html" class="internal-link">GetPosition</a></td>
          <td>获取位置</td>
          <td>返回形状的 x 和 y 坐标和 ReferencePoint</td>
        </tr>
            <tr>
          <td><a href="./Shape.GetPositionEx.html" class="internal-link">GetPositionEx</a></td>
          <td>获取职位前</td>
          <td>返回指定点的坐标</td>
        </tr>
            <tr>
          <td><a href="./Shape.GetSize.html" class="internal-link">GetSize</a></td>
          <td>获取大小</td>
          <td>返回形状的宽度和高度</td>
        </tr>
            <tr>
          <td><a href="./Shape.Group.html" class="internal-link">Group</a></td>
          <td>团体</td>
          <td>创建一组选定的对象</td>
        </tr>
            <tr>
          <td><a href="./Shape.Intersect.html" class="internal-link">Intersect</a></td>
          <td>相交</td>
          <td>创建一个由两个形状的交集组成的形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.IsOnShape.html" class="internal-link">IsOnShape</a></td>
          <td>异形</td>
          <td>查找给定坐标是“内部”、“外部”还是在曲线的边缘</td>
        </tr>
            <tr>
          <td><a href="./Shape.IsTypeAnyOf.html" class="internal-link">IsTypeAnyOf</a></td>
          <td>是任何类型的</td>
          <td>如果指定的形状是列表中包含的类型,则返回 True</td>
        </tr>
            <tr>
          <td><a href="./Shape.Move.html" class="internal-link">Move</a></td>
          <td>移动</td>
          <td>指定水平和或垂直移动形状的距离</td>
        </tr>
            <tr>
          <td><a href="./Shape.MoveToLayer.html" class="internal-link">MoveToLayer</a></td>
          <td>移动到图层</td>
          <td>将形状移动到指定图层</td>
        </tr>
            <tr>
          <td><a href="./Shape.OrderBackOf.html" class="internal-link">OrderBackOf</a></td>
          <td>订单返回</td>
          <td>将形状移到另一个形状后面</td>
        </tr>
            <tr>
          <td><a href="./Shape.OrderBackOne.html" class="internal-link">OrderBackOne</a></td>
          <td>下单一</td>
          <td>将形状按堆叠顺序向后移动一个</td>
        </tr>
            <tr>
          <td><a href="./Shape.OrderForwardOne.html" class="internal-link">OrderForwardOne</a></td>
          <td>订购转发一</td>
          <td>将形状按堆叠顺序向前移动一个</td>
        </tr>
            <tr>
          <td><a href="./Shape.OrderFrontOf.html" class="internal-link">OrderFrontOf</a></td>
          <td>订单前沿</td>
          <td>将形状移动到另一个形状的前面</td>
        </tr>
            <tr>
          <td><a href="./Shape.OrderIsInFrontOf.html" class="internal-link">OrderIsInFrontOf</a></td>
          <td>订单在前面</td>
          <td>确定一个形状是否在另一个形状前面</td>
        </tr>
            <tr>
          <td><a href="./Shape.OrderReverse.html" class="internal-link">OrderReverse</a></td>
          <td>逆序</td>
          <td>颠倒堆叠顺序</td>
        </tr>
            <tr>
          <td><a href="./Shape.OrderToBack.html" class="internal-link">OrderToBack</a></td>
          <td>顺序返回</td>
          <td>按堆叠顺序将形状移到后面</td>
        </tr>
            <tr>
          <td><a href="./Shape.OrderToFront.html" class="internal-link">OrderToFront</a></td>
          <td>订单到前台</td>
          <td>按堆叠顺序将形状移到最前面</td>
        </tr>
            <tr>
          <td><a href="./Shape.PlaceTextInside.html" class="internal-link">PlaceTextInside</a></td>
          <td>在里面放置文本</td>
          <td>将给定文本放置在形状内</td>
        </tr>
            <tr>
          <td><a href="./Shape.Project.html" class="internal-link">Project</a></td>
          <td>项目</td>
          <td>将形状分配给类似 3D 的投影平面之一</td>
        </tr>
            <tr>
          <td><a href="./Shape.RemoveFromContainer.html" class="internal-link">RemoveFromContainer</a></td>
          <td>从容器中移除</td>
          <td>从 PowerClip 对象中删除此形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.RemoveFromSelection.html" class="internal-link">RemoveFromSelection</a></td>
          <td>从选择中删除</td>
          <td>从选择中删除一个形状;如果形状是选择形状则不起作用</td>
        </tr>
            <tr>
          <td><a href="./Shape.ReplaceWith.html" class="internal-link">ReplaceWith</a></td>
          <td>用。。。来代替</td>
          <td>记录形状替换事务,以便用户可以撤消它</td>
        </tr>
            <tr>
          <td><a href="./Shape.Rotate.html" class="internal-link">Rotate</a></td>
          <td>旋转</td>
          <td>通过将数量添加到当前旋转值来旋转形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.RotateEx.html" class="internal-link">RotateEx</a></td>
          <td>旋转式</td>
          <td>通过指定旋转中心和角度来旋转对象</td>
        </tr>
            <tr>
          <td><a href="./Shape.Scallop.html" class="internal-link">Scallop</a></td>
          <td>扇贝</td>
          <td>在形状的每个尖角上创建扇贝</td>
        </tr>
            <tr>
          <td><a href="./Shape.Separate.html" class="internal-link">Separate</a></td>
          <td>分离</td>
          <td>分隔已组合或来自链接组的形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.SetBoundingBox.html" class="internal-link">SetBoundingBox</a></td>
          <td>设置边界框</td>
          <td>移动形状并调整其大小以适合特定边界框(区域和位置)</td>
        </tr>
            <tr>
          <td><a href="./Shape.SetMatrix.html" class="internal-link">SetMatrix</a></td>
          <td>设置矩阵</td>
          <td>将新值应用于已经历转换的形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.SetPosition.html" class="internal-link">SetPosition</a></td>
          <td>设置位置</td>
          <td>将形状移动到特定位置</td>
        </tr>
            <tr>
          <td><a href="./Shape.SetPositionEx.html" class="internal-link">SetPositionEx</a></td>
          <td>设置位置Ex</td>
          <td>将形状移动到指定点的坐标</td>
        </tr>
            <tr>
          <td><a href="./Shape.SetRotationCenter.html" class="internal-link">SetRotationCenter</a></td>
          <td>设置旋转中心</td>
          <td>指定形状旋转中心的水平和垂直位置</td>
        </tr>
            <tr>
          <td><a href="./Shape.SetSize.html" class="internal-link">SetSize</a></td>
          <td>设置大小</td>
          <td>拉伸形状以适应特定的宽度和高度</td>
        </tr>
            <tr>
          <td><a href="./Shape.SetSizeEx.html" class="internal-link">SetSizeEx</a></td>
          <td>设置大小前</td>
          <td>使用锚点设置形状大小</td>
        </tr>
            <tr>
          <td><a href="./Shape.Skew.html" class="internal-link">Skew</a></td>
          <td>偏斜</td>
          <td>指定一个角度(垂直和或水平)来倾斜一个形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.SkewEx.html" class="internal-link">SkewEx</a></td>
          <td>斜交</td>
          <td>通过指定中心、水平和垂直角度来倾斜形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.SnapPointsOfType.html" class="internal-link">SnapPointsOfType</a></td>
          <td>捕捉点类型</td>
          <td>返回给定类型的捕捉点的集合</td>
        </tr>
            <tr>
          <td><a href="./Shape.StepAndRepeat.html" class="internal-link">StepAndRepeat</a></td>
          <td>分步重复</td>
          <td>创建形状的多个副本</td>
        </tr>
            <tr>
          <td><a href="./Shape.Stretch.html" class="internal-link">Stretch</a></td>
          <td>拉紧</td>
          <td>指定一个值以相对于其参考点水平和或垂直拉伸形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.StretchEx.html" class="internal-link">StretchEx</a></td>
          <td>伸展运动</td>
          <td>使用锚点拉伸形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.TransformMatrix.html" class="internal-link">TransformMatrix</a></td>
          <td>变换矩阵</td>
          <td>通过将其乘以给定矩阵来修改形状的变换矩阵</td>
        </tr>
            <tr>
          <td><a href="./Shape.Trim.html" class="internal-link">Trim</a></td>
          <td>修剪</td>
          <td>从另一个对象修剪形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.Ungroup.html" class="internal-link">Ungroup</a></td>
          <td>取消组合</td>
          <td>取消组合一组形状</td>
        </tr>
            <tr>
          <td><a href="./Shape.UngroupAll.html" class="internal-link">UngroupAll</a></td>
          <td>取消组合所有</td>
          <td>取消组合所有形状,包括嵌套组</td>
        </tr>
            <tr>
          <td><a href="./Shape.UngroupAllEx.html" class="internal-link">UngroupAllEx</a></td>
          <td>取消所有前任</td>
          <td>取消组合所有子组并将取消组合的对象作为形状范围返回</td>
        </tr>
            <tr>
          <td><a href="./Shape.UngroupEx.html" class="internal-link">UngroupEx</a></td>
          <td>取消分组</td>
          <td>将未分组的对象作为形状范围返回</td>
        </tr>
            <tr>
          <td><a href="./Shape.Unproject.html" class="internal-link">Unproject</a></td>
          <td>取消项目</td>
          <td>从形状中删除投影变换</td>
        </tr>
            <tr>
          <td><a href="./Shape.Weld.html" class="internal-link">Weld</a></td>
          <td>焊接</td>
          <td>将形状焊接到另一个对象</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
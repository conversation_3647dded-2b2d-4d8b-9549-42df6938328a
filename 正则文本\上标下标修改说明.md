# CorelDRAW VBA 上标下标功能修改说明

## 问题描述
原代码在处理上标和下标时，使用了不正确的方法 `tr.FontPropertiesInRange(1, tr.Characters.Count).Position`，导致"对象不支持该属性或方法"的错误。

## 解决方案
按照用户要求的思路，实现了两阶段处理：
1. 在文本匹配替换过程中收集需要应用上标/下标的文本范围信息
2. 在替换完成后，参考 `文本上下标.bas` 的方法统一处理上标/下标格式

## 主要修改内容

### 1. 添加数据结构
```vba
' 用于存储需要应用上标/下标的文本范围信息
Private Type TextRangeInfo
    Shape As Shape
    StartPos As Long
    Length As Long
    Text As String
End Type

Private SuperscriptRanges() As TextRangeInfo
Private SubscriptRanges() As TextRangeInfo
Private SuperscriptCount As Long
Private SubscriptCount As Long
Private currentProcessingShape As Shape
```

### 2. 修改替换流程
在 `tihuan_Click()` 事件中：
- 添加了初始化上标/下标范围数组的调用
- 在替换完成后添加了应用上标/下标格式的调用

### 3. 修改格式应用逻辑
在 `ApplyChangeFormatIfEnabled()` 函数中：
- 移除了直接应用上标/下标的代码
- 改为收集需要处理的文本范围信息

### 4. 新增辅助函数

#### InitializeScriptRanges()
初始化上标/下标范围数组，重置计数器。

#### CollectScriptRange(tr As TextRange, isSuper As Boolean)
收集需要应用上标/下标的文本范围信息，包括：
- 所属的Shape对象
- 在文本中的起始位置
- 文本长度
- 文本内容

#### GetParentShape(tr As TextRange) As Shape
获取TextRange所属的Shape对象，使用全局变量 `currentProcessingShape`。

#### ApplyScriptFormatting()
参考 `文本上下标.bas` 的实现，统一应用上标/下标格式：
- 使用正确的方法：`Shape.Text.FontPropertiesInRange(startPos, length).Position`
- 支持取消格式功能
- 包含错误处理

### 5. 修改文本处理流程
在 `myReplaceText()` 函数中：
- 添加了设置 `currentProcessingShape` 的代码，确保能正确获取当前处理的Shape对象

## 技术要点

### 正确的上标/下标设置方法
```vba
' 设置上标
shape.Text.FontPropertiesInRange(startPos, length).Position = cdrSuperscriptFontPosition

' 设置下标  
shape.Text.FontPropertiesInRange(startPos, length).Position = cdrSubscriptFontPosition

' 取消格式
shape.Text.FontPropertiesInRange(startPos, length).Position = cdrNormalFontPosition
```

### 两阶段处理的优势
1. **避免冲突**：在文本替换过程中不直接修改格式，避免可能的冲突
2. **准确定位**：收集完整的位置信息，确保格式应用到正确的文本范围
3. **统一处理**：参考成熟的实现方法，提高可靠性
4. **错误处理**：集中的错误处理，便于调试和维护

## 使用说明
1. 勾选"上标"或"下标"复选框
2. 输入要查找的文本
3. 输入替换文本（可以相同）
4. 点击"替换"按钮
5. 系统会先执行文本替换，然后自动应用上标/下标格式

## 调试信息
代码中包含详细的Debug.Print语句，可以在VBA编辑器的立即窗口中查看执行过程和错误信息。

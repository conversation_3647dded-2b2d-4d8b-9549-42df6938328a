<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>ShapeRange.Group</title> 
                        </head><body>
                        <div><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><a href="./ShapeRange.html" class="internal-link"><strong>ShapeRange</strong></a></div>
<div class="content-fragment-header"><a href="./ShapeRange.Group.html" class="internal-link"><strong>ShapeRange.Group</strong></a></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">从范围内的所有形状创建一个组</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <div class="content-fragment-header">语法:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
      <div class="code-block"><span class="keyword">Function</span><span class="space"> </span> <span class="identifier">Group</span><span class="punctuation">(</span><span class="punctuation">)</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./Shape.html" class="internal-link">Shape</a></span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>Group</b> 方法对形状范围内的所有形状进行分组,并返回一个 <a href="./Shape.html">Shape</a> 对象作为对该组的引用。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div id="fragment-1725"></div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1730" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,19">
<div class="content-fragment-content">


      <div class="content-fragment-header">示例:</div>
            <div style="margin-bottom: 1em">下面的 VBA 示例创建一个拼图游戏。</div>
        <div><pre style="code">Sub Test()<br>
 Dim s As Shape, sp As SubPath, sbmp As Shape<br>
 Dim sr As New ShapeRange, n As Long<br>
 Dim x As Double, y As Double, sx As Double, sy As Double<br>
 Set s = ActiveLayer.CreateCurve<br>
 Set sp = s.Curve.CreateSubPath(1.351, 8.545)<br>
 sp.AppendCurveSegment False, 1.351, 8.926, 0.127, 89.901, 0.127, -64.56<br>
 sp.AppendCurveSegment False, 1.156, 8.952, 0.066, 115.44, 0.066, -48.906<br>
 sp.AppendCurveSegment False, 1.156, 9.15, 0.065, 131.09, 0.065, -133.149<br>
 sp.AppendCurveSegment False, 1.351, 9.163, 0.065, 46.846, 0.065, -116.315<br>
 sp.AppendCurveSegment False, 1.351, 9.545, 0.127, 63.683, 0.127, -89.902<br>
 sp.AppendCurveSegment False, 0.976, 9.545, 0.125, 179.951, 0.125, 25.612<br>
 sp.AppendCurveSegment False, 0.96, 9.342, 0.063, -154.391, 0.063, 40.943<br>
 sp.AppendCurveSegment False, 0.767, 9.339, 0.067, -139.06, 0.067, -41.987<br>
 sp.AppendCurveSegment False, 0.752, 9.547, 0.063, 138.014, 0.065, -33.906<br>
 sp.AppendCurveSegment False, 0.351, 9.545, 0.134, 146.087, 0.134, 0.045<br>
 sp.AppendCurveSegment False, 0.351, 9.163, 0.127, -90#, 0.127, 63.681<br>
 sp.AppendCurveSegment False, 0.156, 9.15, 0.065, -116.317, 0.065, 46.846<br>
 sp.AppendCurveSegment False, 0.156, 8.952, 0.065, -133.152, 0.065, 131.093<br>
 sp.AppendCurveSegment False, 0.351, 8.926, 0.066, -48.906, 0.066, 115.439<br>
 sp.AppendCurveSegment False, 0.351, 8.545, 0.127, -64.561, 0.127, 90#<br>
 sp.AppendCurveSegment False, 0.752, 8.547, 0.134, 0.002, 0.134, 146.087<br>
 sp.AppendCurveSegment False, 0.767, 8.339, 0.065, -33.908, 0.063, 138.012<br>
 sp.AppendCurveSegment False, 0.96, 8.342, 0.067, -41.987, 0.067, -139.058<br>
 sp.AppendCurveSegment False, 0.976, 8.545, 0.063, 40.943, 0.063, -154.388<br>
 sp.AppendCurveSegment False, 1.351, 8.545, 0.125, 25.613, 0.125, 179.998<br>
 sp.Closed = True<br>
 For n = 1 To 7<br>
 sr.Add s.Duplicate<br>
 s.Move 1, 0<br>
 Next n<br>
 sr.Add s<br>
 sr.AddRange sr.Duplicate(0, -1)<br>
 sr.AddRange sr.Duplicate(0, -2)<br>
 Set s = sr.Group<br>
 s.GetBoundingBox x, y, sx, sy<br>
 Set sbmp = ActiveLayer.CreateRectangle2(x, y, sx, sy)<br>
 sbmp.Fill.ApplyTextureFill "Gouache wash", "Samples"<br>
 Set sbmp = sbmp.ConvertToBitmapEx(cdrRGBColorImage, , , 120)<br>
 sbmp.AddToPowerClip s<br>
 s.Ungroup<br>
 For Each s In sr<br>
 s.Move (Rnd() - 0.5) * 0.25, (Rnd() - 0.5) * 0.25<br>
 Next s<br>
End Sub</pre></div>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>SnapPoints</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./SnapPoints.html" class="internal-link">SnapPoints</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>SnapPoints class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">快照点类</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">SnapPoints</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>SnapPoints</b> 类表示捕捉点(<a href="./SnapPoint.html">SnapPoint</a>对象)的集合。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./SnapPoints._NewEnum.html" class="internal-link">_NewEnum</a></td>
          <td>_New枚举</td>
          <td>返回一个遍历集合的枚举器</td>
        </tr>
            <tr>
          <td><a href="./SnapPoints.All.html" class="internal-link">All</a></td>
          <td>全部</td>
          <td>返回集合中所有捕捉点的列表</td>
        </tr>
            <tr>
          <td><a href="./SnapPoints.Application.html" class="internal-link">Application</a></td>
          <td>应用</td>
          <td>获取点集合所属的应用程序</td>
        </tr>
            <tr>
          <td><a href="./SnapPoints.Count.html" class="internal-link">Count</a></td>
          <td>数数</td>
          <td>获取点集合中的点数</td>
        </tr>
            <tr>
          <td><a href="./SnapPoints.Item.html" class="internal-link">Item</a></td>
          <td>物品</td>
          <td>获取对指定点的引用,以 1 为基数</td>
        </tr>
            <tr>
          <td><a href="./SnapPoints.Parent.html" class="internal-link">Parent</a></td>
          <td>家长</td>
          <td>获取形状父级</td>
        </tr>
            <tr>
          <td><a href="./SnapPoints.Selection.html" class="internal-link">Selection</a></td>
          <td>选择</td>
          <td>返回选定捕捉点的列表</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1726" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,15">
<div class="content-fragment-content">


      <div class="content-fragment-header">方法:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./SnapPoints.AddUserSnapPoint.html" class="internal-link">AddUserSnapPoint</a></td>
          <td>添加用户捕捉点</td>
          <td>添加用户捕捉点</td>
        </tr>
            <tr>
          <td><a href="./SnapPoints.AddUserSnapPointEx.html" class="internal-link">AddUserSnapPointEx</a></td>
          <td>添加用户捕捉点EX</td>
          <td>添加用户捕捉点</td>
        </tr>
            <tr>
          <td><a href="./SnapPoints.Auto.html" class="internal-link">Auto</a></td>
          <td>汽车</td>
          <td>返回形状的自动捕捉点</td>
        </tr>
            <tr>
          <td><a href="./SnapPoints.BBox.html" class="internal-link">BBox</a></td>
          <td>盒子</td>
          <td>查找指定类型的边界框捕捉点</td>
        </tr>
            <tr>
          <td><a href="./SnapPoints.ClearSelection.html" class="internal-link">ClearSelection</a></td>
          <td>清空选项</td>
          <td>取消选择所有捕捉点</td>
        </tr>
            <tr>
          <td><a href="./SnapPoints.Edge.html" class="internal-link">Edge</a></td>
          <td>边缘</td>
          <td>查找具有给定参数的边缘捕捉点</td>
        </tr>
            <tr>
          <td><a href="./SnapPoints.FindClosest.html" class="internal-link">FindClosest</a></td>
          <td>查找最近的</td>
          <td>查找给定类型的最近捕捉点</td>
        </tr>
            <tr>
          <td><a href="./SnapPoints.Object.html" class="internal-link">Object</a></td>
          <td>目的</td>
          <td>查找指定类型的对象捕捉点</td>
        </tr>
            <tr>
          <td><a href="./SnapPoints.Range.html" class="internal-link">Range</a></td>
          <td>范围</td>
          <td>创建由给定 ID 引用的捕捉点列表</td>
        </tr>
            <tr>
          <td><a href="./SnapPoints.User.html" class="internal-link">User</a></td>
          <td>用户</td>
          <td>按 ID 查找用户捕捉点</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
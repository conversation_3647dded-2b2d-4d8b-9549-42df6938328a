<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>Shape.ConvertToBitmapEx</title> 
                        </head><body>
                        <div><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><a href="./Shape.html" class="internal-link"><strong>Shape</strong></a></div>
<div class="content-fragment-header"><a href="./Shape.ConvertToBitmapEx.html" class="internal-link"><strong>Shape.ConvertToBitmapEx</strong></a></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">将形状转换为位图</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <div class="content-fragment-header">语法:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
      <div class="code-block"><span class="keyword">Function</span><span class="space"> </span> <span class="identifier">ConvertToBitmapEx</span><span class="punctuation">(</span><span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">Mode</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./cdrImageType.html" class="internal-link">cdrImageType</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="namespace">cdrRGBColorImage</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">Dithered</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Boolean</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="keyword">False</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">Transparent</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Boolean</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="keyword">False</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">Resolution</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Long</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="number">72</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">AntiAliasing</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./cdrAntiAliasingType.html" class="internal-link">cdrAntiAliasingType</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="namespace">cdrNormalAntiAliasing</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">UseColorProfile</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Boolean</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="keyword">True</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">AlwaysOverprintBlack</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Boolean</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="keyword">False</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">OverprintBlackLimit</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Long</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="number">95</span><span class="punctuation">)</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./Shape.html" class="internal-link">Shape</a></span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1721" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,10">
<div class="content-fragment-content">


      <div class="content-fragment-header">参数:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名称</th>
          <th>类型</th>
          <th>描述</th>
        </tr>
            <tr>
          <td>Mode</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./cdrImageType.html" class="internal-link">cdrImageType</a></span></div>
                      </td>
          <td><div>{cdrImageType|指定图像类型,并返回 cdrImageType。</div></td>
        </tr>
            <tr>
          <td>Dithered</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Boolean</span></div>
                      </td>
          <td><div>如果设置为 <b>True</b> (<b>-1</b>),则启用抖动。</div></td>
        </tr>
            <tr>
          <td>Transparent</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Boolean</span></div>
                      </td>
          <td><div>如果设置为 <b>True</b> (<b>-1</b>),则启用透明背景。</div></td>
        </tr>
            <tr>
          <td>Resolution</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Long</span></div>
                      </td>
          <td><div>指定分辨率。</div></td>
        </tr>
            <tr>
          <td>AntiAliasing</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./cdrAntiAliasingType.html" class="internal-link">cdrAntiAliasingType</a></span></div>
                      </td>
          <td><div>指定抗锯齿,返回值 <a href="./cdrAntiAliasingType.html">cdrAntiAliasingType</a>。</div></td>
        </tr>
            <tr>
          <td>UseColorProfile</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Boolean</span></div>
                      </td>
          <td><div>如果设置为 True (<b>-1</b>),则使用颜色配置文件。</div></td>
        </tr>
            <tr>
          <td>AlwaysOverprintBlack</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Boolean</span></div>
                      </td>
          <td><div></div></td>
        </tr>
            <tr>
          <td>OverprintBlackLimit</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Long</span></div>
                      </td>
          <td><div></div></td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>ConvertToBitmapEx</b> 将形状转换为位图。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div id="fragment-1725"></div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1730" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,19">
<div class="content-fragment-content">


      <div class="content-fragment-header">示例:</div>
            <div style="margin-bottom: 1em">下面的 VBA 示例创建一个矩形和一个椭圆。 然后它将椭圆转换为具有透明背景的 CMYK 位图,对其应用阴影,并将其与矩形分组。</div>
        <div><pre style="code">Sub Test()<br>
 Dim r As Shape, s As Shape<br>
 Set r = ActiveLayer.CreateRectangle(0, 0, 5, 5)<br>
 r.Fill.UniformColor.CMYKAssign 100, 0, 0, 0<br>
 Set s = ActiveLayer.CreateEllipse2(3, 3, 2)<br>
 s.Fill.UniformColor.CMYKAssign 0, 100, 100, 0<br>
 Set s = s.ConvertToBitmapEx(cdrCMYKColorImage, False, True)<br>
 s.CreateDropShadow cdrDropShadowFlat, 80, 10, 0.5, -0.5, CreateCMYKColor(0, 50, 50, 50)<br>
 r.CreateSelection<br>
 s.Selected = True<br>
 ActiveSelection.Group<br>
End Sub</pre></div>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
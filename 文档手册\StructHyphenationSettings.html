<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>StructHyphenationSettings</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./StructHyphenationSettings.html" class="internal-link">StructHyphenationSettings</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>StructHyphenationSettings class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;"></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">StructHyphenationSettings</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>StructHyphenationSettings</b> 类表示文本对象的断字设置。</div>
                <div>您可以使用 Visual Basic 中的 <b>New</b> 关键字创建 <b>StructHyphenationSettings</b> 对象。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./StructHyphenationSettings.BreakAllCapWords.html" class="internal-link">BreakAllCapWords</a></td>
          <td>打破所有帽子词</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructHyphenationSettings.BreakCapitalized.html" class="internal-link">BreakCapitalized</a></td>
          <td>休息大写</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructHyphenationSettings.HotZone.html" class="internal-link">HotZone</a></td>
          <td>热区</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructHyphenationSettings.MinCharactersAfter.html" class="internal-link">MinCharactersAfter</a></td>
          <td>之后的最少字符</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructHyphenationSettings.MinCharactersBefore.html" class="internal-link">MinCharactersBefore</a></td>
          <td>最少字符数</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructHyphenationSettings.MinWordLength.html" class="internal-link">MinWordLength</a></td>
          <td>最小字长</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructHyphenationSettings.UseAutomaticHyphenation.html" class="internal-link">UseAutomaticHyphenation</a></td>
          <td>使用自动连字符</td>
          <td></td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
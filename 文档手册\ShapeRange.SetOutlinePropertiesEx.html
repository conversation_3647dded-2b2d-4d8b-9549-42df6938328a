<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>ShapeRange.SetOutlinePropertiesEx</title> 
                        </head><body>
                        <div><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><a href="./ShapeRange.html" class="internal-link"><strong>ShapeRange</strong></a></div>
<div class="content-fragment-header"><a href="./ShapeRange.SetOutlinePropertiesEx.html" class="internal-link"><strong>ShapeRange.SetOutlinePropertiesEx</strong></a></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">设置所有轮廓属性</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <div class="content-fragment-header">语法:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
      <div class="code-block"><span class="keyword">Sub</span><span class="space"> </span> <span class="identifier">SetOutlinePropertiesEx</span><span class="punctuation">(</span><span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">Width</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Double</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="number">-1</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">Style</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./OutlineStyle.html" class="internal-link">OutlineStyle</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="keyword">Nothing</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">Color</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./Color.html" class="internal-link">Color</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="keyword">Nothing</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">StartArrow</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./ArrowHead.html" class="internal-link">ArrowHead</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="keyword">Nothing</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">EndArrow</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./ArrowHead.html" class="internal-link">ArrowHead</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="keyword">Nothing</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">BehindFill</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./cdrTriState.html" class="internal-link">cdrTriState</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="namespace">cdrUndefined</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">ScaleWithShape</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="https://community.coreldraw.com/sdk/api/draw/17/e/cdrTriState.html" class="internal-link">cdrTriState</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="namespace">cdrUndefined</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">LineCaps</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="https://community.coreldraw.com/sdk/api/draw/17/e/cdrOutlineLineCaps.html" class="internal-link">cdrOutlineLineCaps</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="namespace">cdrOutlineUndefinedLineCaps</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">LineJoin</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./cdrOutlineLineJoin.html" class="internal-link">cdrOutlineLineJoin</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="namespace">cdrOutlineUndefinedLineJoin</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">NibAngle</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Double</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="number">-9999</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">NibStretch</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Long</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="number">0</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">DashDotLength</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Double</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="number">-1</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">PenWidth</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Double</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="number">0</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">MiterLimit</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Double</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="number">0</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">Justification</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./cdrOutlineJustification.html" class="internal-link">cdrOutlineJustification</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="namespace">cdrOutlineJustificationUndefined</span><span class="punctuation">)</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1721" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,10">
<div class="content-fragment-content">


      <div class="content-fragment-header">参数:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名称</th>
          <th>类型</th>
          <th>描述</th>
        </tr>
            <tr>
          <td>Width</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Double</span></div>
                      </td>
          <td><div>指定轮廓的宽度,以磅为单位。</div></td>
        </tr>
            <tr>
          <td>Style</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./OutlineStyle.html" class="internal-link">OutlineStyle</a></span></div>
                      </td>
          <td><div>指定轮廓样式。</div></td>
        </tr>
            <tr>
          <td>Color</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./Color.html" class="internal-link">Color</a></span></div>
                      </td>
          <td><div>指定轮廓颜色。</div></td>
        </tr>
            <tr>
          <td>StartArrow</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./ArrowHead.html" class="internal-link">ArrowHead</a></span></div>
                      </td>
          <td><div>指定轮廓的开始箭头。</div></td>
        </tr>
            <tr>
          <td>EndArrow</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./ArrowHead.html" class="internal-link">ArrowHead</a></span></div>
                      </td>
          <td><div>指定轮廓的结束箭头。</div></td>
        </tr>
            <tr>
          <td>BehindFill</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./cdrTriState.html" class="internal-link">cdrTriState</a></span></div>
                      </td>
          <td><div>指定填充是否在轮廓后面。</div></td>
        </tr>
            <tr>
          <td>ScaleWithShape</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./cdrTriState.html" class="internal-link">cdrTriState</a></span></div>
                      </td>
          <td><div>指定轮廓是否保持形状的大小比例。</div></td>
        </tr>
            <tr>
          <td>LineCaps</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./cdrOutlineLineCaps.html" class="internal-link">cdrOutlineLineCaps</a></span></div>
                      </td>
          <td><div>指定开放路径轮廓的端点样式。</div></td>
        </tr>
            <tr>
          <td>LineJoin</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./cdrOutlineLineJoin.html" class="internal-link">cdrOutlineLineJoin</a></span></div>
                      </td>
          <td><div>指定轮廓中相互交叉的线条的外观,并返回 <a href="./cdrOutlineLineJoin.html">cdrOutlineLineJoin</一个>。</div></td>
        </tr>
            <tr>
          <td>NibAngle</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Double</span></div>
                      </td>
          <td><div>为书法轮廓指定笔尖相对于绘图表面的方向。</div></td>
        </tr>
            <tr>
          <td>NibStretch</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Long</span></div>
                      </td>
          <td><div>为书法轮廓指定笔尖的粗细。</div></td>
        </tr>
            <tr>
          <td>DashDotLength</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Double</span></div>
                      </td>
          <td><div>指定轮廓的点划线长度。</div></td>
        </tr>
            <tr>
          <td>PenWidth</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Double</span></div>
                      </td>
          <td><div>指定轮廓的笔宽。</div></td>
        </tr>
            <tr>
          <td>MiterLimit</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Double</span></div>
                      </td>
          <td><div></div></td>
        </tr>
            <tr>
          <td>Justification</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./cdrOutlineJustification.html" class="internal-link">cdrOutlineJustification</a></span></div>
                      </td>
          <td><div>指定轮廓对齐设置。</div></td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>SetOutlinePropertiesEx</b> 方法设置形状范围内每个形状的轮廓属性,包括轮廓对齐方式。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div id="fragment-1725"></div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1730" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,19">
<div class="content-fragment-content">


      <div class="content-fragment-header">示例:</div>
            <div style="margin-bottom: 1em">以下 VBA 示例将宽度为 <b>0.05</b>" 的蓝色虚线轮廓应用于活动页面上的所有椭圆。</div>
        <div><pre style="code">Sub Test()<br>
 Dim sr As ShapeRange<br>
 Set sr = ActivePage.FindShapes(, cdrEllipseShape)<br>
 sr.SetOutlinePropertiesEx 0.15, Justification: = cdrOutlineJustificationOutside<br>
End Sub</pre></div>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
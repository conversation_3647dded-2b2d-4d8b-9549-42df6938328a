<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>StyleFill</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./StyleFill.html" class="internal-link">StyleFill</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>StyleFill class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">样式填充类</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">StyleFill</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>StyleFill </b> 类表示样式的填充属性。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./StyleFill.FlipColors.html" class="internal-link">FlipColors</a></td>
          <td>翻转颜色</td>
          <td>指定渐变颜色是否必须反转</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.FountainAnisotropic.html" class="internal-link">FountainAnisotropic</a></td>
          <td>渐变各向异性</td>
          <td>指定渐变填充是否可以不按比例拉伸和倾斜</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.FountainBlendAcceleration.html" class="internal-link">FountainBlendAcceleration</a></td>
          <td>渐变混合加速</td>
          <td>指定颜色混合加速</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.FountainBlendType.html" class="internal-link">FountainBlendType</a></td>
          <td>渐变混合类型</td>
          <td>指定第一对渐变色的混合类型</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.FountainCenterXOffset.html" class="internal-link">FountainCenterXOffset</a></td>
          <td>渐变中心 X 偏移</td>
          <td>指定渐变填充中心的水平偏移</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.FountainCenterYOffset.html" class="internal-link">FountainCenterYOffset</a></td>
          <td>渐变中心YOffset</td>
          <td>指定渐变填充中心的垂直偏移</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.FountainFillType.html" class="internal-link">FountainFillType</a></td>
          <td>渐变填充类型</td>
          <td>指定渐变填充类型</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.FountainScaleX.html" class="internal-link">FountainScaleX</a></td>
          <td>渐变规模X</td>
          <td>指定渐变填充的水平缩放因子</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.FountainScaleY.html" class="internal-link">FountainScaleY</a></td>
          <td>渐变规模Y</td>
          <td>指定渐变填充的垂直比例因子</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.FountainSpreadMethod.html" class="internal-link">FountainSpreadMethod</a></td>
          <td>渐变传播法</td>
          <td>指定渐变填充的扩散方法</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.FountainSteps.html" class="internal-link">FountainSteps</a></td>
          <td>渐变台阶</td>
          <td>指定渐变填充的步数,如果无限制则为零</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.MidPoint.html" class="internal-link">MidPoint</a></td>
          <td>中点</td>
          <td>指定第一对渐变色的中点</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.MirrorFill.html" class="internal-link">MirrorFill</a></td>
          <td>镜像填充</td>
          <td>指定是否镜像填充(已废弃)</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.MirrorFillX.html" class="internal-link">MirrorFillX</a></td>
          <td>镜像填充X</td>
          <td>指定是否水平镜像填充平铺</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.MirrorFillY.html" class="internal-link">MirrorFillY</a></td>
          <td>镜像填充</td>
          <td>指定是否垂直镜像填充瓦片</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.Overprint.html" class="internal-link">Overprint</a></td>
          <td>套印</td>
          <td>指定是否应叠印填充</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.PostScriptName.html" class="internal-link">PostScriptName</a></td>
          <td>后记名</td>
          <td>指定后记填充名称</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.PrimaryColor.html" class="internal-link">PrimaryColor</a></td>
          <td>原色</td>
          <td>指定填充渐变色标的颜色</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.PrimaryOpacity.html" class="internal-link">PrimaryOpacity</a></td>
          <td>主要不透明度</td>
          <td>指定第一个色标的不透明度</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.RotationAngle.html" class="internal-link">RotationAngle</a></td>
          <td>旋转角度</td>
          <td>指定填充旋转角度</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.SecondaryColor.html" class="internal-link">SecondaryColor</a></td>
          <td>次要颜色</td>
          <td>指定最后一个渐变色标的颜色</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.SecondaryOpacity.html" class="internal-link">SecondaryOpacity</a></td>
          <td>次要不透明度</td>
          <td>指定最后一个色标的不透明度</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.SkewAngle.html" class="internal-link">SkewAngle</a></td>
          <td>斜角</td>
          <td>指定填充倾斜角度</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.Style.html" class="internal-link">Style</a></td>
          <td>风格</td>
          <td>返回填充样式的通用样式对象</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.TileHeight.html" class="internal-link">TileHeight</a></td>
          <td>瓷砖高度</td>
          <td>指定平铺高度</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.TileOffset.html" class="internal-link">TileOffset</a></td>
          <td>平铺偏移</td>
          <td>以瓦片宽度/高度的百分比指定瓦片偏移</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.TileOffsetType.html" class="internal-link">TileOffsetType</a></td>
          <td>平铺偏移类型</td>
          <td>指定平铺偏移类型</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.TileOriginX.html" class="internal-link">TileOriginX</a></td>
          <td>瓷砖起源X</td>
          <td>指定水平平铺原点</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.TileOriginY.html" class="internal-link">TileOriginY</a></td>
          <td>瓷砖原点 Y</td>
          <td>指定垂直平铺原点</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.TileWidth.html" class="internal-link">TileWidth</a></td>
          <td>平铺宽度</td>
          <td>指定平铺宽度</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.TransformWithShape.html" class="internal-link">TransformWithShape</a></td>
          <td>随形状变换</td>
          <td>指定填充是否与形状一起变换</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.Type.html" class="internal-link">Type</a></td>
          <td>类型</td>
          <td>指定填充类型</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.WindingFill.html" class="internal-link">WindingFill</a></td>
          <td>绕组填充</td>
          <td>指定渲染填充时的缠绕规则</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1726" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,15">
<div class="content-fragment-content">


      <div class="content-fragment-header">方法:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./StyleFill.LoadFill.html" class="internal-link">LoadFill</a></td>
          <td>加载填充</td>
          <td>从文件加载填充并将其应用于样式</td>
        </tr>
            <tr>
          <td><a href="./StyleFill.SaveFill.html" class="internal-link">SaveFill</a></td>
          <td>保存填充</td>
          <td>将填充样式保存到文件</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
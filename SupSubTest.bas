Option Explicit

Public Sub TestSetSuperscript(s As Shape)
    If s Is Nothing Then
        Debug.Print "TestSetSuperscript: 传入的 Shape 对象为 Nothing"
        Exit Sub
    End If
    
    If s.Type <> cdrTextShape Then
        Debug.Print "TestSetSuperscript: 传入的 Shape 不是文本对象"
        Exit Sub
    End If
    
    If s.Text Is Nothing Then
        Debug.Print "TestSetSuperscript: s.Text 对象为 Nothing"
        Exit Sub
    End If
    If s.Text.Story Is Nothing Then
        Debug.Print "TestSetSuperscript: s.Text.Story 对象为 Nothing"
        Exit Sub
    End If
    
    Debug.Print "TestSetSuperscript: 原始文本内容: '" & s.Text.Story.Text & "'"
    Debug.Print "TestSetSuperscript: 尝试设置整个文本为上标"
    
    Dim fontProps As Object
    On Error Resume Next ' 允许我们检查错误
    Set fontProps = s.Text.FontPropertiesInRange(1, s.Text.Story.Characters.Count)
    
    If Err.Number <> 0 Then
        Debug.Print "TestSetSuperscript: 获取 FontPropertiesInRange 失败: " & Err.Description
        Err.Clear
        Set fontProps = Nothing
        Exit Sub
    End If
    
    If fontProps Is Nothing Then
        Debug.Print "TestSetSuperscript: FontPropertiesInRange 返回的对象为 Nothing"
        Exit Sub
    End If
    
    Debug.Print "TestSetSuperscript: FontPropertiesInRange 对象类型: " & TypeName(fontProps)
    
    fontProps.Position = cdrSuperscriptFontPosition
    If Err.Number <> 0 Then
        Debug.Print "TestSetSuperscript: 设置上标 Position 失败: " & Err.Description
        Err.Clear
    Else
        Debug.Print "TestSetSuperscript: 上标设置成功"
    End If
    
    On Error GoTo 0 ' 恢复正常的错误处理
    Set fontProps = Nothing
End Sub

Public Sub TestSetSubscript(s As Shape)
    If s Is Nothing Then
        Debug.Print "TestSetSubscript: 传入的 Shape 对象为 Nothing"
        Exit Sub
    End If
    
    If s.Type <> cdrTextShape Then
        Debug.Print "TestSetSubscript: 传入的 Shape 不是文本对象"
        Exit Sub
    End If
    
    If s.Text Is Nothing Then
        Debug.Print "TestSetSubscript: s.Text 对象为 Nothing"
        Exit Sub
    End If
    If s.Text.Story Is Nothing Then
        Debug.Print "TestSetSubscript: s.Text.Story 对象为 Nothing"
        Exit Sub
    End If

    Debug.Print "TestSetSubscript: 原始文本内容: '" & s.Text.Story.Text & "'"
    Debug.Print "TestSetSubscript: 尝试设置整个文本为下标"
    
    Dim fontProps As Object
    On Error Resume Next ' 允许我们检查错误
    Set fontProps = s.Text.FontPropertiesInRange(1, s.Text.Story.Characters.Count)
    
    If Err.Number <> 0 Then
        Debug.Print "TestSetSubscript: 获取 FontPropertiesInRange 失败: " & Err.Description
        Err.Clear
        Set fontProps = Nothing
        Exit Sub
    End If
    
    If fontProps Is Nothing Then
        Debug.Print "TestSetSubscript: FontPropertiesInRange 返回的对象为 Nothing"
        Exit Sub
    End If
    
    Debug.Print "TestSetSubscript: FontPropertiesInRange 对象类型: " & TypeName(fontProps)
    
    fontProps.Position = cdrSubscriptFontPosition
    If Err.Number <> 0 Then
        Debug.Print "TestSetSubscript: 设置下标 Position 失败: " & Err.Description
        Err.Clear
    Else
        Debug.Print "TestSetSubscript: 下标设置成功"
    End If
    
    On Error GoTo 0 ' 恢复正常的错误处理
    Set fontProps = Nothing
End Sub


' 调用示例 (您可以在CorelDRAW的VBA编辑器中运行这些宏进行测试):
Sub RunSuperscriptTest()
   Dim s As Shape
   Set s = ActiveLayer.CreateArtisticText(0, 0, "测试文本")
   If s Is Nothing Then
       MsgBox "创建文本失败"
       Exit Sub
   End If
   TestSetSuperscript s
End Sub

Sub RunSubscriptTest()
   Dim s As Shape
   Set s = ActiveLayer.CreateArtisticText(0, 0, "测试文本")
   If s Is Nothing Then
       MsgBox "创建文本失败"
       Exit Sub
   End If
   TestSetSubscript s
End Sub

Sub RunSuperscriptTestOnSelection()
   If ActiveSelection.Shapes.Count = 0 Then
       MsgBox "请先选择一个文本对象"
       Exit Sub
   End If
   Dim s As Shape
   Set s = ActiveSelection.Shapes(1)
   TestSetSuperscript s
End Sub

Sub RunSubscriptTestOnSelection()
   If ActiveSelection.Shapes.Count = 0 Then
       MsgBox "请先选择一个文本对象"
       Exit Sub
   End If
   Dim s As Shape
   Set s = ActiveSelection.Shapes(1)
   TestSetSubscript s
End Sub
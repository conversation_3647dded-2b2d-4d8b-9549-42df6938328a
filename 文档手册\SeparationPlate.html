<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>SeparationPlate</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./SeparationPlate.html" class="internal-link">SeparationPlate</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>SeparationPlate class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">分隔板类</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">SeparationPlate</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>SeparationPlate</b> 类表示一个分隔板对象。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./SeparationPlate.Angle.html" class="internal-link">Angle</a></td>
          <td>角度</td>
          <td>获取或设置此色板的屏幕角度</td>
        </tr>
            <tr>
          <td><a href="./SeparationPlate.Color.html" class="internal-link">Color</a></td>
          <td>颜色</td>
          <td>返回分隔板的颜色名称</td>
        </tr>
            <tr>
          <td><a href="./SeparationPlate.Enabled.html" class="internal-link">Enabled</a></td>
          <td>启用</td>
          <td>确定是否要输出当前的分色板</td>
        </tr>
            <tr>
          <td><a href="./SeparationPlate.Frequency.html" class="internal-link">Frequency</a></td>
          <td>频率</td>
          <td>获取或设置此色板的屏幕频率</td>
        </tr>
            <tr>
          <td><a href="./SeparationPlate.OverprintGraphic.html" class="internal-link">OverprintGraphic</a></td>
          <td>叠印图形</td>
          <td>决定是否应在此色板上套印图形</td>
        </tr>
            <tr>
          <td><a href="./SeparationPlate.OverprintText.html" class="internal-link">OverprintText</a></td>
          <td>叠印文本</td>
          <td>确定是否应在此色板上套印文本</td>
        </tr>
            <tr>
          <td><a href="./SeparationPlate.Type.html" class="internal-link">Type</a></td>
          <td>类型</td>
          <td>留作将来使用</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>Shape.Next</title> 
                        </head><body>
                        <div><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><a href="./Shape.html" class="internal-link"><strong>Shape</strong></a></div>
<div class="content-fragment-header"><a href="./Shape.Next.html" class="internal-link"><strong>Shape.Next</strong></a></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">返回下一个形状</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <div class="content-fragment-header">语法:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
      <div class="code-block"><span class="keyword">Property</span><span class="space"> </span> <span class="keyword">Get</span><span class="space"> </span> <span class="identifier">Next</span><span class="punctuation">(</span><span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">Level</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./cdrShapeLevel.html" class="internal-link">cdrShapeLevel</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="namespace">cdrLevelPage</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">EnterGroups</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Boolean</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="keyword">false</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">Loop</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Boolean</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="keyword">true</span><span class="punctuation">)</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./Shape.html" class="internal-link">Shape</a></span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1721" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,10">
<div class="content-fragment-content">


      <div class="content-fragment-header">参数:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名称</th>
          <th>类型</th>
          <th>描述</th>
        </tr>
            <tr>
          <td>Level</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./cdrShapeLevel.html" class="internal-link">cdrShapeLevel</a></span></div>
                      </td>
          <td><div>指定是否将范围限制为当前组、图层、页面或文档。</div></td>
        </tr>
            <tr>
          <td>EnterGroups</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Boolean</span></div>
                      </td>
          <td><div>指定如何处理分组形状。如果此值设置为 <b>False</b>,组将被视为单独的形状,忽略组内的任何形状。如果设置为 <b>True</b>,仍然返回组形状。</div></td>
        </tr>
            <tr>
          <td>Loop</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Boolean</span></div>
                      </td>
          <td><div>指定在搜索期间如何循环。如果此值设置为 <b>True</b> 并且到达范围内的最后一个形状,则返回组、层或文档的第一个形状。否则,返回 <b>Nothing</b>。</div></td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>Next</b> 属性返回文档中的下一个形状。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div id="fragment-1725"></div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1730" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,19">
<div class="content-fragment-content">


      <div class="content-fragment-header">示例:</div>
            <div style="margin-bottom: 1em">以下 VBA 示例选择当前图层上的下一个形状,并根据需要输入组。</div>
        <div><pre style="code">Sub Test()<br>
 Dim s As Shape<br>
 Set s = ActiveShape<br>
 If s Is Nothing Then<br>
 MsgBox "Nothing is selected."<br>
 Else<br>
 s.Next(cdrLevelLayer, True, True).CreateSelection<br>
 End If<br>
End Sub</pre></div>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
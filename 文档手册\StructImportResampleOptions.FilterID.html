<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>StructImportResampleOptions.FilterID</title> 
                        </head><body>
                        <div><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><a href="./StructImportResampleOptions.html" class="internal-link"><strong>StructImportResampleOptions</strong></a></div>
<div class="content-fragment-header"><a href="./StructImportResampleOptions.FilterID.html" class="internal-link"><strong>StructImportResampleOptions.FilterID</strong></a></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;"></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <div class="content-fragment-header">语法:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
      <div class="code-block"><span class="keyword">Property</span><span class="space"> </span> <span class="keyword">Get</span><span class="space"> </span> <span class="identifier">FilterID</span><span class="punctuation">(</span><span class="punctuation">)</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Long</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>FilterID</b> 属性指定用于在导入时重新采样的过滤器 ID。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div id="fragment-1725"></div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1730" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,19">
<div class="content-fragment-content">


      <div class="content-fragment-header">示例:</div>
            <div style="margin-bottom: 1em">下面的 VBA 示例创建一个新的 resample-options 类。然后它会导入一个 JPEG 文件并根据 <a href="./StructImportResampleOptions.CustomData.html">StructImportResampleOptions.CustomData</a> 的值对其重新采样。在应用重新采样之前，会显示图像的原始设置。</div>
        <div style="margin-bottom: 1em">要试用此代码示例,请将以下代码行复制到名为 <b>cResample</b> 的新类模块中:</div>
        <div><pre style="code">Option Explicit<br>
Implements IImportResampleHandler<br>
Private Function IImportResampleHandler_Resample(Options As IStructImportResampleOptions) As Boolean<br>
 Dim s As String<br>
 s = "Original Settings: " &amp; vbCr<br>
 s = s &amp; "DpiX: " &amp; Options.DpiX &amp; vbCr<br>
 s = s &amp; "DpiY: " &amp; Options.DpiY &amp; vbCr<br>
 s = s &amp; "FileName: " &amp; Options.FileName &amp; vbCr<br>
 s = s &amp; "Filter: " &amp; Options.FilterID &amp; vbCr<br>
 ' Display the original settings in a message box<br>
 MsgBox s<br>
 ' If the custom data is set to 8, then resample half of the original; otherwise 1.5<br>
 If Options.CustomData = 8 Then<br>
 Options.Height = Options.Height / 2<br>
 Options.Width = Options.Width / 2<br>
 Else<br>
 Options.Height = Options.Height / 1.5<br>
 Options.Width = Options.Width / 1.5<br>
 End If<br>
End Function</pre></div>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>Shape.Intersect</title> 
                        </head><body>
                        <div><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><a href="./Shape.html" class="internal-link"><strong>Shape</strong></a></div>
<div class="content-fragment-header"><a href="./Shape.Intersect.html" class="internal-link"><strong>Shape.Intersect</strong></a></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">创建一个由两个形状的交集组成的形状</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <div class="content-fragment-header">语法:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
      <div class="code-block"><span class="keyword">Function</span><span class="space"> </span> <span class="identifier">Intersect</span><span class="punctuation">(</span><span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">TargetShape</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./Shape.html" class="internal-link">Shape</a></span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">LeaveSource</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Boolean</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="keyword">True</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">LeaveTarget</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Boolean</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="keyword">True</span><span class="punctuation">)</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./Shape.html" class="internal-link">Shape</a></span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1721" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,10">
<div class="content-fragment-content">


      <div class="content-fragment-header">参数:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名称</th>
          <th>类型</th>
          <th>描述</th>
        </tr>
            <tr>
          <td>TargetShape</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./Shape.html" class="internal-link">Shape</a></span></div>
                      </td>
          <td><div>指定相交的形状</div></td>
        </tr>
            <tr>
          <td>LeaveSource</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Boolean</span></div>
                      </td>
          <td><div>指定相交完成后是否保留相交的形状。</div></td>
        </tr>
            <tr>
          <td>LeaveTarget</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Boolean</span></div>
                      </td>
          <td><div>指定相交完成后是否保留相交形状。</div></td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>Intersect</b> 方法创建一个由两个形状的交集组成的形状。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div id="fragment-1725"></div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1730" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,19">
<div class="content-fragment-content">


      <div class="content-fragment-header">示例:</div>
            <div style="margin-bottom: 1em">以下 VBA 示例为加色模型 (RGB) 创建了一个颜色图。三个圆圈被填满,每个圆圈都有红色、绿色和蓝色。在圆圈的交叉处,生成的颜色混合填充区域:中间为白色,每对圆圈之间的区域为青色、洋红色和黄色。</div>
        <div><pre style="code">Sub Test()<br>
 Dim s(0 To 2) As Shape<br>
 Dim si(0 To 2) As Shape<br>
 Dim sm As Shape<br>
 Dim x As Double, y As Double<br>
 Dim i As Long, n As Long<br>
 Dim r As Long, g As Long, b As Long<br>
 Dim c1 As Color, c2 As Color<br>
 For i = 0 To 2<br>
 x = ActivePage.SizeWidth / 2 + 1 * Cos(i * 2.09439507)<br>
 y = ActivePage.SizeHeight / 2 + 1 * Sin(i * 2.09439507)<br>
 Set s(i) = ActiveLayer.CreateEllipse2(x, y, 1.5)<br>
 r = -255 * (i = 0)<br>
 g = -255 * (i = 1)<br>
 b = -255 * (i = 2)<br>
 s(i).Fill.UniformColor.RGBAssign r, g, b<br>
 Next i<br>
 For i = 0 To 2<br>
 n = (i + 1) Mod 3<br>
 Set si(i) = s(i).Intersect(s(n))<br>
 Set c1 = s(i).Fill.UniformColor<br>
 Set c2 = s(n).Fill.UniformColor<br>
 r = c1.RGBRed + c2.RGBRed<br>
 g = c1.RGBGreen + c2.RGBGreen<br>
 b = c1.RGBBlue + c2.RGBBlue<br>
 si(i).Fill.UniformColor.RGBAssign r, g, b<br>
 Next i<br>
 Set sm = si(1).Intersect(si(2))<br>
 sm.Fill.UniformColor.RGBAssign 255, 255, 255<br>
End Sub</pre></div>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
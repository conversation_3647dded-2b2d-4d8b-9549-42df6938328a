<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>ShapeRange</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./ShapeRange.html" class="internal-link">ShapeRange</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>ShapeRange class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">表示 Shape 对象的动态数组(或范围)。特定于形状的属性和方法可以应用于形状范围内的每个形状。</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">ShapeRange</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>ShapeRange</b> 类表示 <a href="./Shape.html">Shape</a> 对象的动态数组（或范围）。特定于形状的属性和方法可以应用于形状范围内的每个形状。</div>
                <div>您可以在 Visual Basic 中使用 <b>New</b> 关键字来创建 <b>ShapeRange</b> 对象。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./ShapeRange._NewEnum.html" class="internal-link">_NewEnum</a></td>
          <td>_New枚举</td>
          <td>返回一个遍历集合的枚举器</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Application.html" class="internal-link">Application</a></td>
          <td>应用</td>
          <td>获取形状范围所属的应用程序</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.BottomY.html" class="internal-link">BottomY</a></td>
          <td>底部Y</td>
          <td>返回范围内形状边界框底部边缘的 Y 坐标</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.BoundingBox.html" class="internal-link">BoundingBox</a></td>
          <td>边界框</td>
          <td>返回形状周围的边界矩形</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.CenterX.html" class="internal-link">CenterX</a></td>
          <td>中心X</td>
          <td>获取或设置形状中心的水平位置</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.CenterY.html" class="internal-link">CenterY</a></td>
          <td>中心Y</td>
          <td>获取或设置形状中心的垂直位置</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Count.html" class="internal-link">Count</a></td>
          <td>数数</td>
          <td>获取范围内的形状数</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.FirstShape.html" class="internal-link">FirstShape</a></td>
          <td>第一形状</td>
          <td>返回范围内的第一个形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Item.html" class="internal-link">Item</a></td>
          <td>物品</td>
          <td>获取对指定形状的引用,以 1 为基数</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.LastShape.html" class="internal-link">LastShape</a></td>
          <td>最后形状</td>
          <td>返回范围内的最后一个形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.LeftX.html" class="internal-link">LeftX</a></td>
          <td>左X</td>
          <td>返回范围内形状边界框左边缘的 X 坐标</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Parent.html" class="internal-link">Parent</a></td>
          <td>家长</td>
          <td>获取形状范围是其子级的父级</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.PositionX.html" class="internal-link">PositionX</a></td>
          <td>位置X</td>
          <td>根据文档的参考点获取或设置页面形状范围的水平位置(X)</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.PositionY.html" class="internal-link">PositionY</a></td>
          <td>位置Y</td>
          <td>根据文档的参考点获取或设置页面形状范围的垂直位置(Y)</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ReverseRange.html" class="internal-link">ReverseRange</a></td>
          <td>反向范围</td>
          <td>返回另一个形状范围,其中包含给定范围内的所有形状,以相反的顺序</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.RightX.html" class="internal-link">RightX</a></td>
          <td>右X</td>
          <td>返回范围内形状边界框右边缘的 X 坐标</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.RotationCenterX.html" class="internal-link">RotationCenterX</a></td>
          <td>旋转中心X</td>
          <td>获取或设置页面上形??状范围的水平旋转中心 (X)</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.RotationCenterY.html" class="internal-link">RotationCenterY</a></td>
          <td>旋转中心Y</td>
          <td>获取或设置页面上形??状范围的垂直旋转中心 (Y)</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Shapes.html" class="internal-link">Shapes</a></td>
          <td>形状</td>
          <td>从范围内返回形状的集合</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.SizeHeight.html" class="internal-link">SizeHeight</a></td>
          <td>尺寸高度</td>
          <td>获取或设置页面形状范围的高度</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.SizeWidth.html" class="internal-link">SizeWidth</a></td>
          <td>尺寸宽度</td>
          <td>获取或设置页面上形??状范围的宽度</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.TopY.html" class="internal-link">TopY</a></td>
          <td>顶部 Y</td>
          <td>返回范围内形状边界框上边缘的 Y 坐标</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1726" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,15">
<div class="content-fragment-content">


      <div class="content-fragment-header">方法:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Add.html" class="internal-link">Add</a></td>
          <td>添加</td>
          <td>在范围内添加形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.AddRange.html" class="internal-link">AddRange</a></td>
          <td>添加范围</td>
          <td>将形状范围添加到范围中</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.AddToPowerClip.html" class="internal-link">AddToPowerClip</a></td>
          <td>添加到PowerClip</td>
          <td>将此形状范围添加到电源夹</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.AddToSelection.html" class="internal-link">AddToSelection</a></td>
          <td>添加到选择</td>
          <td>将 shaperange 添加到选择</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.AffineTransform.html" class="internal-link">AffineTransform</a></td>
          <td>仿射变换</td>
          <td>将由变换矩阵分量定义的仿射变换应用于相对于变换中心点的形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.AlignAndDistribute.html" class="internal-link">AlignAndDistribute</a></td>
          <td>对齐和分布</td>
          <td>在范围内对齐和/或分布形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.AlignRangeToGrid.html" class="internal-link">AlignRangeToGrid</a></td>
          <td>对齐范围到网格</td>
          <td>将形状范围与网格对齐</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.AlignRangeToPage.html" class="internal-link">AlignRangeToPage</a></td>
          <td>对齐范围到页面</td>
          <td>将形状范围与页面边缘对齐</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.AlignRangeToPageCenter.html" class="internal-link">AlignRangeToPageCenter</a></td>
          <td>对齐范围到页面中心</td>
          <td>将形状范围与页面中心对齐</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.AlignRangeToPoint.html" class="internal-link">AlignRangeToPoint</a></td>
          <td>对齐范围到点</td>
          <td>将形状范围与指定的坐标对齐</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.AlignRangeToShape.html" class="internal-link">AlignRangeToShape</a></td>
          <td>对齐范围到形状</td>
          <td>将形状范围与指定形状对齐</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.AlignRangeToShapeRange.html" class="internal-link">AlignRangeToShapeRange</a></td>
          <td>对齐范围以形成形状范围</td>
          <td>将形状范围与指定形状范围的边界框对齐</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.AlignToGrid.html" class="internal-link">AlignToGrid</a></td>
          <td>对齐网格</td>
          <td>将范围的所有形状与网格对齐</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.AlignToPage.html" class="internal-link">AlignToPage</a></td>
          <td>对齐到页面</td>
          <td>将范围的所有形状与页面边缘对齐</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.AlignToPageCenter.html" class="internal-link">AlignToPageCenter</a></td>
          <td>对齐到页面中心</td>
          <td>将范围的所有形状与页面中心对齐</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.AlignToPoint.html" class="internal-link">AlignToPoint</a></td>
          <td>对齐到点</td>
          <td>将范围的所有形状与指定的坐标对齐</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.AlignToShape.html" class="internal-link">AlignToShape</a></td>
          <td>对齐形状</td>
          <td>将范围内的所有形状与指定形状对齐</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.AlignToShapeRange.html" class="internal-link">AlignToShapeRange</a></td>
          <td>对齐形状范围</td>
          <td>将范围的所有形状与指定形状范围的边界框对齐</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.All.html" class="internal-link">All</a></td>
          <td>全部</td>
          <td>返回当前形状范围的副本</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.AllExcluding.html" class="internal-link">AllExcluding</a></td>
          <td>全部不包括</td>
          <td>从当前范围内的所有形状中创建一个新的形状范围,除了指定的形状列表</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ApplyCustomHatchFill.html" class="internal-link">ApplyCustomHatchFill</a></td>
          <td>应用自定义孵化填充</td>
          <td>将自定义影线填充应用于范围内的形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ApplyEffectBCI.html" class="internal-link">ApplyEffectBCI</a></td>
          <td>应用效果BCI</td>
          <td>将亮度-对比度-强度颜色效果应用于范围内的形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ApplyEffectColorBalance.html" class="internal-link">ApplyEffectColorBalance</a></td>
          <td>应用效果颜色平衡</td>
          <td>对范围内的形状应用色彩平衡效果</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ApplyEffectGamma.html" class="internal-link">ApplyEffectGamma</a></td>
          <td>申请效果伽玛</td>
          <td>将伽马颜色校正效果应用于范围内的形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ApplyEffectHSL.html" class="internal-link">ApplyEffectHSL</a></td>
          <td>应用效果HSL</td>
          <td>将色相-饱和度-亮度颜色效果应用于范围内的形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ApplyEffectInvert.html" class="internal-link">ApplyEffectInvert</a></td>
          <td>申请效果反转</td>
          <td>反转范围内形状的颜色</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ApplyEffectPosterize.html" class="internal-link">ApplyEffectPosterize</a></td>
          <td>申请效果海报</td>
          <td>将色调分离颜色效果应用于范围内的形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ApplyFill.html" class="internal-link">ApplyFill</a></td>
          <td>应用填充</td>
          <td>将指定的填充应用于范围内的对象</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ApplyFountainFill.html" class="internal-link">ApplyFountainFill</a></td>
          <td>应用渐变填充</td>
          <td>将渐变填充应用于范围内的所有形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ApplyHatchFill.html" class="internal-link">ApplyHatchFill</a></td>
          <td>应用HatchFill</td>
          <td>将库中的预设影线填充应用到范围内的形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ApplyNoFill.html" class="internal-link">ApplyNoFill</a></td>
          <td>没有填充</td>
          <td>对范围内的所有形状不应用填充</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ApplyOutline.html" class="internal-link">ApplyOutline</a></td>
          <td>应用轮廓</td>
          <td>将指定的轮廓应用于范围内的对象</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ApplyPatternFill.html" class="internal-link">ApplyPatternFill</a></td>
          <td>应用图案填充</td>
          <td>将图案填充应用于范围内的所有形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ApplyPostscriptFill.html" class="internal-link">ApplyPostscriptFill</a></td>
          <td>应用后记填写</td>
          <td>对范围内的所有形状应用 PostScript 填充</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ApplyTextureFill.html" class="internal-link">ApplyTextureFill</a></td>
          <td>应用纹理填充</td>
          <td>将纹理填充应用于范围内的所有形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ApplyUniformFill.html" class="internal-link">ApplyUniformFill</a></td>
          <td>应用均匀填充</td>
          <td>对范围内的所有形状应用均匀填充</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.BreakApart.html" class="internal-link">BreakApart</a></td>
          <td>分手</td>
          <td>将范围内的所有对象分开</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.BreakApartEx.html" class="internal-link">BreakApartEx</a></td>
          <td>分解前</td>
          <td>将范围内的所有对象分开,并将断开的形状作为形状范围返回</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Chamfer.html" class="internal-link">Chamfer</a></td>
          <td>倒角</td>
          <td>在范围内形状的每个尖角上创建倒角</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ClearEffect.html" class="internal-link">ClearEffect</a></td>
          <td>清除效果</td>
          <td>清除形状范围内形状的效果。</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ClearTransformations.html" class="internal-link">ClearTransformations</a></td>
          <td>清除转换</td>
          <td>清除应用于此范围内形状的所有旋转、拉伸和倾斜变换</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Clone.html" class="internal-link">Clone</a></td>
          <td>克隆</td>
          <td>克隆 ShapeRange 并返回对该范围的引用</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Combine.html" class="internal-link">Combine</a></td>
          <td>结合</td>
          <td>将所有形状组合在一起</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ConvertOutlineToObject.html" class="internal-link">ConvertOutlineToObject</a></td>
          <td>将轮廓转换为对象</td>
          <td>将轮廓转换为对象</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ConvertToBitmap.html" class="internal-link">ConvertToBitmap</a></td>
          <td>转换为位图</td>
          <td>将范围内的所有形状栅格化为位图对象</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ConvertToBitmapEx.html" class="internal-link">ConvertToBitmapEx</a></td>
          <td>转换为位图</td>
          <td>将形状范围光栅化为位图文件并返回一个形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ConvertToCurves.html" class="internal-link">ConvertToCurves</a></td>
          <td>转换为曲线</td>
          <td>将范围内的所有形状转换为曲线</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ConvertToSymbol.html" class="internal-link">ConvertToSymbol</a></td>
          <td>转换为符号</td>
          <td>将形状转换为符号</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Copy.html" class="internal-link">Copy</a></td>
          <td>复制</td>
          <td>将 shaperange 复制到剪贴板</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.CopyPropertiesFrom.html" class="internal-link">CopyPropertiesFrom</a></td>
          <td>复制属性</td>
          <td>从另一个形状复制填充、轮廓和/或文本属性</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.CopyToLayer.html" class="internal-link">CopyToLayer</a></td>
          <td>复制到图层</td>
          <td>将范围内的所有形状复制到指定图层</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.CountAnyOfType.html" class="internal-link">CountAnyOfType</a></td>
          <td>计算任何类型</td>
          <td>返回范围内特定类型的形状数</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.CreateBoundary.html" class="internal-link">CreateBoundary</a></td>
          <td>创建边界</td>
          <td>在 ShapeRange 中的项目周围创建边界形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.CreateDocumentFrom.html" class="internal-link">CreateDocumentFrom</a></td>
          <td>创建文档自</td>
          <td>创建包含范围内形状的文档副本</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.CreateSelection.html" class="internal-link">CreateSelection</a></td>
          <td>创建选择</td>
          <td>从形状范围中进行选择</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.CustomCommand.html" class="internal-link">CustomCommand</a></td>
          <td>自定义命令</td>
          <td>对给定的形状执行自定义命令</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Cut.html" class="internal-link">Cut</a></td>
          <td>切</td>
          <td>从页面中删除 shaperange,并将其放置到剪贴板上</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Delete.html" class="internal-link">Delete</a></td>
          <td>删除</td>
          <td>删除范围内的形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.DeleteItem.html" class="internal-link">DeleteItem</a></td>
          <td>删除项目</td>
          <td>从形状范围中删除指定的形状并将其从文档中删除</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Distribute.html" class="internal-link">Distribute</a></td>
          <td>分发</td>
          <td>分布 ShapeRange 中的形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Duplicate.html" class="internal-link">Duplicate</a></td>
          <td>复制</td>
          <td>复制整个形状范围</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.EqualDivide.html" class="internal-link">EqualDivide</a></td>
          <td>等分</td>
          <td>将 ShapeRange 中的对象分成相等的部分</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Exists.html" class="internal-link">Exists</a></td>
          <td>存在</td>
          <td>确定给定的形状是否存在于范围内</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.ExistsAnyOfType.html" class="internal-link">ExistsAnyOfType</a></td>
          <td>存在任何类型</td>
          <td>确定范围是否包含特定类型的任何形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Fillet.html" class="internal-link">Fillet</a></td>
          <td>鱼片</td>
          <td>在范围内形状的每个尖角上创建圆角</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.FindAnyOfType.html" class="internal-link">FindAnyOfType</a></td>
          <td>找到任何类型</td>
          <td>返回范围中包含的特定类型的形状列表</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Flip.html" class="internal-link">Flip</a></td>
          <td>翻动</td>
          <td>镜像范围内的所有形状,水平或垂直</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.GetBoundingBox.html" class="internal-link">GetBoundingBox</a></td>
          <td>获取边界框</td>
          <td>获取相对于其左下角的形状范围边界框</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.GetLinkedShapes.html" class="internal-link">GetLinkedShapes</a></td>
          <td>获取LinkedShapes</td>
          <td>获取以某种方式链接到范围内形状的所有形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.GetOverprintFillState.html" class="internal-link">GetOverprintFillState</a></td>
          <td>获取叠印填充状态</td>
          <td>返回形状范围的叠印填充状态标志</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.GetOverprintOutlineState.html" class="internal-link">GetOverprintOutlineState</a></td>
          <td>获取套印轮廓状态</td>
          <td>返回形状范围的叠印轮廓状态标志</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.GetPosition.html" class="internal-link">GetPosition</a></td>
          <td>获取位置</td>
          <td>返回形状范围的 x y 位置</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.GetPositionEx.html" class="internal-link">GetPositionEx</a></td>
          <td>获取职位前</td>
          <td>返回指定点的坐标</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.GetSize.html" class="internal-link">GetSize</a></td>
          <td>获取大小</td>
          <td>返回形状范围的宽度和高度</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Group.html" class="internal-link">Group</a></td>
          <td>团体</td>
          <td>从范围内的所有形状创建一个组</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.IndexOf.html" class="internal-link">IndexOf</a></td>
          <td>指数</td>
          <td>获取形状的索引</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Lock.html" class="internal-link">Lock</a></td>
          <td>锁</td>
          <td>锁定范围内的所有形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Move.html" class="internal-link">Move</a></td>
          <td>移动</td>
          <td>指定距离以水平和或垂直移动形状范围</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.MoveToLayer.html" class="internal-link">MoveToLayer</a></td>
          <td>移动到图层</td>
          <td>将范围内的所有形状移动到指定图层</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.OrderBackOf.html" class="internal-link">OrderBackOf</a></td>
          <td>订单返回</td>
          <td>通过将形状移到另一个形状的后面来排列形状的堆叠顺序</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.OrderBackOne.html" class="internal-link">OrderBackOne</a></td>
          <td>下单一</td>
          <td>通过将形状向后移动一个来排列形状的堆叠顺序</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.OrderForwardOne.html" class="internal-link">OrderForwardOne</a></td>
          <td>订购转发一</td>
          <td>通过将形状向前移动一个来排列形状的堆叠顺序</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.OrderFrontOf.html" class="internal-link">OrderFrontOf</a></td>
          <td>订单前沿</td>
          <td>通过将形状移动到另一个形状的前面来排列形状的堆叠顺序</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.OrderReverse.html" class="internal-link">OrderReverse</a></td>
          <td>逆序</td>
          <td>逆转</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.OrderToBack.html" class="internal-link">OrderToBack</a></td>
          <td>顺序返回</td>
          <td>通过将形状移到后面来排列形状的堆叠顺序</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.OrderToFront.html" class="internal-link">OrderToFront</a></td>
          <td>订单到前台</td>
          <td>通过将形状移到前面来排列形状的堆叠顺序</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Project.html" class="internal-link">Project</a></td>
          <td>项目</td>
          <td>将所有形状分配给我们的 3D 投影平面之一</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Range.html" class="internal-link">Range</a></td>
          <td>范围</td>
          <td>从当前范围的指定形状列表中创建一个新的形状范围</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Remove.html" class="internal-link">Remove</a></td>
          <td>消除</td>
          <td>从范围中删除指定的形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.RemoveAll.html" class="internal-link">RemoveAll</a></td>
          <td>移除所有</td>
          <td>删除范围内的所有形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.RemoveFromContainer.html" class="internal-link">RemoveFromContainer</a></td>
          <td>从容器中移除</td>
          <td>从电源夹中删除此形状范围</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.RemoveFromSelection.html" class="internal-link">RemoveFromSelection</a></td>
          <td>从选择中删除</td>
          <td>从当前选择中删除范围内的形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.RemoveRange.html" class="internal-link">RemoveRange</a></td>
          <td>删除范围</td>
          <td>从形状范围中删除一系列形状。</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.RestoreCloneLink.html" class="internal-link">RestoreCloneLink</a></td>
          <td>还原克隆链接</td>
          <td>恢复形状范围内形状的克隆链接。</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Rotate.html" class="internal-link">Rotate</a></td>
          <td>旋转</td>
          <td>通过将数量添加到当前旋转值来旋转形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.RotateEx.html" class="internal-link">RotateEx</a></td>
          <td>旋转式</td>
          <td>EX-通过将数量添加到当前旋转值来旋转形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Scallop.html" class="internal-link">Scallop</a></td>
          <td>扇贝</td>
          <td>在范围内形状的每个尖角上创建扇贝</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.SetBoundingBox.html" class="internal-link">SetBoundingBox</a></td>
          <td>设置边界框</td>
          <td>移动和调整形状以适应特定的边界框,(区域和位置)</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.SetFillMode.html" class="internal-link">SetFillMode</a></td>
          <td>设置填充模式</td>
          <td>指定范围内形状的填充模式</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.SetOutlineProperties.html" class="internal-link">SetOutlineProperties</a></td>
          <td>设置轮廓属性</td>
          <td>设置所有轮廓属性</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.SetOutlinePropertiesEx.html" class="internal-link">SetOutlinePropertiesEx</a></td>
          <td>设置轮廓属性 Ex</td>
          <td>设置所有轮廓属性</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.SetPixelAlignedRendering.html" class="internal-link">SetPixelAlignedRendering</a></td>
          <td>设置像素对齐渲染</td>
          <td>将对齐到像素属性设置为范围内的形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.SetPosition.html" class="internal-link">SetPosition</a></td>
          <td>设置位置</td>
          <td>将形状范围移动到特定位置</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.SetPositionEx.html" class="internal-link">SetPositionEx</a></td>
          <td>设置位置Ex</td>
          <td>将形状移动到指定点的坐标</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.SetRotationCenter.html" class="internal-link">SetRotationCenter</a></td>
          <td>设置旋转中心</td>
          <td>设置形状范围的旋转中心</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.SetSize.html" class="internal-link">SetSize</a></td>
          <td>设置大小</td>
          <td>拉伸形状范围以适应特定的宽度和高度</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.SetSizeEx.html" class="internal-link">SetSizeEx</a></td>
          <td>设置大小前</td>
          <td>使用锚点设置形状范围大小</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Skew.html" class="internal-link">Skew</a></td>
          <td>偏斜</td>
          <td>以水平和垂直角度倾斜范围内的所有形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.SkewEx.html" class="internal-link">SkewEx</a></td>
          <td>斜交</td>
          <td>通过指定中心点和角度来倾斜范围内的所有形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Sort.html" class="internal-link">Sort</a></td>
          <td>种类</td>
          <td>根据 CQL 条件表达式对范围内的形状进行排序</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.StepAndRepeat.html" class="internal-link">StepAndRepeat</a></td>
          <td>分步重复</td>
          <td>在范围内创建多个形状副本</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Stretch.html" class="internal-link">Stretch</a></td>
          <td>拉紧</td>
          <td>拉伸形状范围内的形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.StretchEx.html" class="internal-link">StretchEx</a></td>
          <td>伸展运动</td>
          <td>使用锚点拉伸形状范围内的形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Ungroup.html" class="internal-link">Ungroup</a></td>
          <td>取消组合</td>
          <td>取消组合范围内的一级组</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.UngroupAll.html" class="internal-link">UngroupAll</a></td>
          <td>取消组合所有</td>
          <td>取消组合所有形状,包括嵌套组</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.UngroupAllEx.html" class="internal-link">UngroupAllEx</a></td>
          <td>取消所有前任</td>
          <td>取消组合所有已分组的形状并将未分组的对象作为形状范围返回</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.UngroupEx.html" class="internal-link">UngroupEx</a></td>
          <td>取消分组</td>
          <td>取消组合范围内的一级组并将取消组合的形状作为范围返回</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Unlock.html" class="internal-link">Unlock</a></td>
          <td>开锁</td>
          <td>解锁范围内的所有形状</td>
        </tr>
            <tr>
          <td><a href="./ShapeRange.Unproject.html" class="internal-link">Unproject</a></td>
          <td>取消项目</td>
          <td>从一系列形状中移除投影变换</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
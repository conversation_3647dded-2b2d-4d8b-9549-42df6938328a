<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>Spread</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./Spread.html" class="internal-link">Spread</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>Spread class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">传播类</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">Spread</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>Spread</b> 类表示页面展开的设置。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./Spread.ActiveLayer.html" class="internal-link">ActiveLayer</a></td>
          <td>活动层</td>
          <td>返回当前页面展开的活动层</td>
        </tr>
            <tr>
          <td><a href="./Spread.AllLayers.html" class="internal-link">AllLayers</a></td>
          <td>所有图层</td>
          <td>返回当前页面展开的所有层</td>
        </tr>
            <tr>
          <td><a href="./Spread.BottomY.html" class="internal-link">BottomY</a></td>
          <td>底部Y</td>
          <td>返回页面展开底部边缘的 Y 坐标</td>
        </tr>
            <tr>
          <td><a href="./Spread.BoundingBox.html" class="internal-link">BoundingBox</a></td>
          <td>边界框</td>
          <td>返回页面展开的边界框</td>
        </tr>
            <tr>
          <td><a href="./Spread.CenterX.html" class="internal-link">CenterX</a></td>
          <td>中心X</td>
          <td>返回页面展开中心的 X 坐标</td>
        </tr>
            <tr>
          <td><a href="./Spread.CenterY.html" class="internal-link">CenterY</a></td>
          <td>中心Y</td>
          <td>返回页面展开中心的 Y 坐标</td>
        </tr>
            <tr>
          <td><a href="./Spread.DesktopLayer.html" class="internal-link">DesktopLayer</a></td>
          <td>桌面层</td>
          <td>从页面展开返回桌面层</td>
        </tr>
            <tr>
          <td><a href="./Spread.GridLayer.html" class="internal-link">GridLayer</a></td>
          <td>网格层</td>
          <td>从页面展开返回网格层</td>
        </tr>
            <tr>
          <td><a href="./Spread.Guides.html" class="internal-link">Guides</a></td>
          <td>指南</td>
          <td>从页面展开返回所有指南</td>
        </tr>
            <tr>
          <td><a href="./Spread.GuidesLayer.html" class="internal-link">GuidesLayer</a></td>
          <td>引导层</td>
          <td>从页面展开返回指南层</td>
        </tr>
            <tr>
          <td><a href="./Spread.Index.html" class="internal-link">Index</a></td>
          <td>指数</td>
          <td>返回当前页面展开的索引</td>
        </tr>
            <tr>
          <td><a href="./Spread.Layers.html" class="internal-link">Layers</a></td>
          <td>图层</td>
          <td>返回当前页面展开的非主图层</td>
        </tr>
            <tr>
          <td><a href="./Spread.LeftX.html" class="internal-link">LeftX</a></td>
          <td>左X</td>
          <td>返回跨页左边缘的 X 坐标</td>
        </tr>
            <tr>
          <td><a href="./Spread.Next.html" class="internal-link">Next</a></td>
          <td>下一个</td>
          <td>返回文档中展开的下一页</td>
        </tr>
            <tr>
          <td><a href="./Spread.Pages.html" class="internal-link">Pages</a></td>
          <td>页面</td>
          <td>返回属于此跨页的页面集合</td>
        </tr>
            <tr>
          <td><a href="./Spread.Previous.html" class="internal-link">Previous</a></td>
          <td>以前的</td>
          <td>返回文档中展开的上一页</td>
        </tr>
            <tr>
          <td><a href="./Spread.RightX.html" class="internal-link">RightX</a></td>
          <td>右X</td>
          <td>返回跨页右边缘的 X 坐标</td>
        </tr>
            <tr>
          <td><a href="./Spread.SelectableShapes.html" class="internal-link">SelectableShapes</a></td>
          <td>可选形状</td>
          <td>返回驻留在页面展开上的可选形状</td>
        </tr>
            <tr>
          <td><a href="./Spread.Shapes.html" class="internal-link">Shapes</a></td>
          <td>形状</td>
          <td>返回驻留在页面展开上的形状</td>
        </tr>
            <tr>
          <td><a href="./Spread.SizeHeight.html" class="internal-link">SizeHeight</a></td>
          <td>尺寸高度</td>
          <td>返回当前页面展开的高度</td>
        </tr>
            <tr>
          <td><a href="./Spread.SizeWidth.html" class="internal-link">SizeWidth</a></td>
          <td>尺寸宽度</td>
          <td>返回当前页面展开的宽度</td>
        </tr>
            <tr>
          <td><a href="./Spread.TopY.html" class="internal-link">TopY</a></td>
          <td>顶部 Y</td>
          <td>返回页面跨页上边缘的 Y 坐标</td>
        </tr>
            <tr>
          <td><a href="./Spread.TreeNode.html" class="internal-link">TreeNode</a></td>
          <td>树节点</td>
          <td>返回代表页面展开的树节点</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1726" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,15">
<div class="content-fragment-content">


      <div class="content-fragment-header">方法:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./Spread.CreateLayer.html" class="internal-link">CreateLayer</a></td>
          <td>创建层</td>
          <td>在跨页上创建一个新层</td>
        </tr>
            <tr>
          <td><a href="./Spread.GetBoundingBox.html" class="internal-link">GetBoundingBox</a></td>
          <td>获取边界框</td>
          <td>返回页面展开的边界框</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
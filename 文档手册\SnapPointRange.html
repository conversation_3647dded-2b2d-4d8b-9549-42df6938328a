<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>SnapPointRange</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./SnapPointRange.html" class="internal-link">SnapPointRange</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>SnapPointRange class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">SnapPointRange 类</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">SnapPointRange</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>SnapPointRange</b> 类表示一系列捕捉点(<a href="./SnapPoint.html">SnapPoint</a>对象)。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./SnapPointRange._NewEnum.html" class="internal-link">_NewEnum</a></td>
          <td>_New枚举</td>
          <td>返回一个遍历集合的枚举器</td>
        </tr>
            <tr>
          <td><a href="./SnapPointRange.Count.html" class="internal-link">Count</a></td>
          <td>数数</td>
          <td>返回列表中的捕捉点数</td>
        </tr>
            <tr>
          <td><a href="./SnapPointRange.Item.html" class="internal-link">Item</a></td>
          <td>物品</td>
          <td>从列表中返回给定的捕捉点</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1726" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,15">
<div class="content-fragment-content">


      <div class="content-fragment-header">方法:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./SnapPointRange.Add.html" class="internal-link">Add</a></td>
          <td>添加</td>
          <td>向列表中添加一个捕捉点</td>
        </tr>
            <tr>
          <td><a href="./SnapPointRange.AddToSelection.html" class="internal-link">AddToSelection</a></td>
          <td>添加到选择</td>
          <td>将捕捉点添加到当前选择</td>
        </tr>
            <tr>
          <td><a href="./SnapPointRange.ChangeDirection.html" class="internal-link">ChangeDirection</a></td>
          <td>改变方向</td>
          <td>更改列表中所有捕捉点的默认连接方向</td>
        </tr>
            <tr>
          <td><a href="./SnapPointRange.CreateSelection.html" class="internal-link">CreateSelection</a></td>
          <td>创建选择</td>
          <td>从列表中的捕捉点创建选择</td>
        </tr>
            <tr>
          <td><a href="./SnapPointRange.Delete.html" class="internal-link">Delete</a></td>
          <td>删除</td>
          <td>删除列表中的所有捕捉点</td>
        </tr>
            <tr>
          <td><a href="./SnapPointRange.Find.html" class="internal-link">Find</a></td>
          <td>寻找</td>
          <td>在列表中查找一个捕捉点</td>
        </tr>
            <tr>
          <td><a href="./SnapPointRange.Move.html" class="internal-link">Move</a></td>
          <td>移动</td>
          <td>将列表中的所有捕捉点移动给定的偏移量</td>
        </tr>
            <tr>
          <td><a href="./SnapPointRange.Remove.html" class="internal-link">Remove</a></td>
          <td>消除</td>
          <td>从列表中删除一个捕捉点(但不删除物理捕捉点)</td>
        </tr>
            <tr>
          <td><a href="./SnapPointRange.RemoveByReference.html" class="internal-link">RemoveByReference</a></td>
          <td>按引用删除</td>
          <td>从列表中删除由参考数据标识的捕捉点</td>
        </tr>
            <tr>
          <td><a href="./SnapPointRange.RemoveFromSelection.html" class="internal-link">RemoveFromSelection</a></td>
          <td>从选择中删除</td>
          <td>从当前选择中删除捕捉点</td>
        </tr>
            <tr>
          <td><a href="./SnapPointRange.SetAutoSnap.html" class="internal-link">SetAutoSnap</a></td>
          <td>设置自动捕捉</td>
          <td>更改列表中所有捕捉点的自动捕捉状态</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
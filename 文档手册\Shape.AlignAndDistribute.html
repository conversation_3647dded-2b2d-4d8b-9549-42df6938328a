<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>Shape.AlignAndDistribute</title> 
                        </head><body>
                        <div><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><a href="./Shape.html" class="internal-link"><strong>Shape</strong></a></div>
<div class="content-fragment-header"><a href="./Shape.AlignAndDistribute.html" class="internal-link"><strong>Shape.AlignAndDistribute</strong></a></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">对齐和/或分布选择中的一个或多个形状</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <div class="content-fragment-header">语法:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
      <div class="code-block"><span class="keyword">Sub</span><span class="space"> </span> <span class="identifier">AlignAndDistribute</span><span class="punctuation">(</span><span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">MethodH</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./cdrAlignDistributeH.html" class="internal-link">cdrAlignDistributeH</a></span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">MethodV</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./cdrAlignDistributeV.html" class="internal-link">cdrAlignDistributeV</a></span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">AlignTo</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./cdrAlignShapesTo.html" class="internal-link">cdrAlignShapesTo</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="namespace">cdrAlignShapesToLastSelected</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">DistributeArea</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./cdrDistributeArea.html" class="internal-link">cdrDistributeArea</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="namespace">cdrDistributeToSelection</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">UseOutline</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Boolean</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="keyword">False</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">TextAlignOrigin</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./cdrTextAlignOrigin.html" class="internal-link">cdrTextAlignOrigin</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="namespace">cdrTextAlignBoundingBox</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">PointX</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Double</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="number">0</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">PointY</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Double</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="number">0</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">DistributeRect</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./Rect.html" class="internal-link">Rect</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="keyword">Nothing</span><span class="punctuation">)</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1721" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,10">
<div class="content-fragment-content">


      <div class="content-fragment-header">参数:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名称</th>
          <th>类型</th>
          <th>描述</th>
        </tr>
            <tr>
          <td>MethodH</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./cdrAlignDistributeH.html" class="internal-link">cdrAlignDistributeH</a></span></div>
                      </td>
          <td><div>指定用于水平对齐和分布对象的方法</div></td>
        </tr>
            <tr>
          <td>MethodV</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./cdrAlignDistributeV.html" class="internal-link">cdrAlignDistributeV</a></span></div>
                      </td>
          <td><div>指定用于垂直对齐和分布对象的方法</div></td>
        </tr>
            <tr>
          <td>AlignTo</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./cdrAlignShapesTo.html" class="internal-link">cdrAlignShapesTo</a></span></div>
                      </td>
          <td><div>指定是否将对象与页面的中心或边缘、网格、最后选择的对象或定义的点对齐</div></td>
        </tr>
            <tr>
          <td>DistributeArea</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./cdrDistributeArea.html" class="internal-link">cdrDistributeArea</a></span></div>
                      </td>
          <td><div>指定分布对象的区域</div></td>
        </tr>
            <tr>
          <td>UseOutline</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Boolean</span></div>
                      </td>
          <td><div>计算对象大小和位置时考虑轮廓厚度</div></td>
        </tr>
            <tr>
          <td>TextAlignOrigin</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./cdrTextAlignOrigin.html" class="internal-link">cdrTextAlignOrigin</a></span></div>
                      </td>
          <td><div>指定对齐文本的原点,例如边界框、第一条基线或最后一条基线</div></td>
        </tr>
            <tr>
          <td>PointX</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Double</span></div>
                      </td>
          <td><div>指定用于对齐和分布的参考点的 x 坐标</div></td>
        </tr>
            <tr>
          <td>PointY</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Double</span></div>
                      </td>
          <td><div>指定用于对齐和分布的参考点的 y 坐标</div></td>
        </tr>
            <tr>
          <td>DistributeRect</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./Rect.html" class="internal-link">Rect</a></span></div>
                      </td>
          <td><div>在指定的矩形内分布对象</div></td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>AlignAndDistribute</b> 方法对齐和/或分布选择中的一个或多个形状。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div id="fragment-1725"></div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
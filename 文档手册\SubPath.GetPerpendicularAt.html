<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>SubPath.GetPerpendicularAt</title> 
                        </head><body>
                        <div><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><a href="./SubPath.html" class="internal-link"><strong>SubPath</strong></a></div>
<div class="content-fragment-header"><a href="./SubPath.GetPerpendicularAt.html" class="internal-link"><strong>SubPath.GetPerpendicularAt</strong></a></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">找到线段上一点的垂线</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <div class="content-fragment-header">语法:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
      <div class="code-block"><span class="keyword">Function</span><span class="space"> </span> <span class="identifier">GetPerpendicularAt</span><span class="punctuation">(</span><span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">Offset</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Double</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="number">0.5</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">OffsetType</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./cdrSegmentOffsetType.html" class="internal-link">cdrSegmentOffsetType</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="namespace">cdrRelativeSegmentOffset</span><span class="punctuation">)</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Double</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1721" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,10">
<div class="content-fragment-content">


      <div class="content-fragment-header">参数:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名称</th>
          <th>类型</th>
          <th>描述</th>
        </tr>
            <tr>
          <td>Offset</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Double</span></div>
                      </td>
          <td><div>以文档单位指定垂线的偏移量。 5.</div></td>
        </tr>
            <tr>
          <td>OffsetType</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./cdrSegmentOffsetType.html" class="internal-link">cdrSegmentOffsetType</a></span></div>
                      </td>
          <td><div>指定偏移类型,并返回 <a href="./cdrSegmentOffsetType.html">cdrSegmentOffsetType</a>。</div></td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>GetPerpendicularAt</b> 方法返回曲线子路径上的点的垂线。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div id="fragment-1725"></div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1730" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,19">
<div class="content-fragment-content">


      <div class="content-fragment-header">示例:</div>
            <div style="margin-bottom: 1em">下面的 VBA 示例为沿选定曲线的子路径均匀分布的几个点绘制垂直和切线矢量。</div>
        <div><pre style="code">Sub Test()<br>
 Dim sp As SubPath<br>
 Dim t As Double<br>
 For Each sp In ActiveShape.Curve.Subpaths<br>
 For t = 0 To 0.9 Step 0.1<br>
 MarkPoint sp, t<br>
 Next t<br>
 Next sp<br>
End Sub<br>
Private Sub MarkPoint(sp As SubPath, t As Double)<br>
 Dim x As Double, y As Double<br>
 Dim dx As Double, dy As Double<br>
 Dim a1 As Double, a2 As Double<br>
 sp.GetPointPositionAt x, y, t, cdrRelativeSegmentOffset<br>
 a1 = sp.GetTangentAt(t, cdrRelativeSegmentOffset) * 3.1415926 / 180<br>
 a2 = sp.GetPerpendicularAt(t, cdrRelativeSegmentOffset) * 3.1415926 / 180<br>
 dx = 0.5 * Cos(a1)<br>
 dy = 0.5 * Sin(a1)<br>
 With ActiveLayer.CreateLineSegment(x, y, x + dx, y + dy)<br>
 .Outline.EndArrow = ArrowHeads(1)<br>
 End With<br>
 dx = 0.5 * Cos(a2)<br>
 dy = 0.5 * Sin(a2)<br>
 With ActiveLayer.CreateLineSegment(x, y, x + dx, y + dy)<br>
 .Outline.EndArrow = ArrowHeads(1)<br>
 End With<br>
End Sub</pre></div>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
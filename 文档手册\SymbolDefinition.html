<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>SymbolDefinition</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./SymbolDefinition.html" class="internal-link">SymbolDefinition</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>SymbolDefinition class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">符号定义类</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">SymbolDefinition</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>SymbolDefinition</b> 类表示符号的定义。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.Description.html" class="internal-link">Description</a></td>
          <td>描述</td>
          <td>指定符号定义的描述</td>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.Editable.html" class="internal-link">Editable</a></td>
          <td>可编辑</td>
          <td>如果可以修改符号定义,则返回 True</td>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.HasBrokenLinks.html" class="internal-link">HasBrokenLinks</a></td>
          <td>有破碎的链接</td>
          <td>如果任何嵌套的子符号与其外部库的链接断开,则返回 True</td>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.HasLinks.html" class="internal-link">HasLinks</a></td>
          <td>有链接</td>
          <td>如果任何子符号链接到外部库,则返回 True</td>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.HasUpdatedLinks.html" class="internal-link">HasUpdatedLinks</a></td>
          <td>已更新链接</td>
          <td>如果符号有任何指向需要更新的外部库的链接,则返回 True</td>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.InstanceCount.html" class="internal-link">InstanceCount</a></td>
          <td>实例计数</td>
          <td>返回插入到当前文档中的此定义的实例数</td>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.Instances.html" class="internal-link">Instances</a></td>
          <td>实例</td>
          <td>返回当前文档中此定义的所有实例的集合</td>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.IsLinkBroken.html" class="internal-link">IsLinkBroken</a></td>
          <td>链接断开</td>
          <td>如果缺少外部库,则返回 True</td>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.IsLinkUpdated.html" class="internal-link">IsLinkUpdated</a></td>
          <td>链接更新了吗</td>
          <td>如果需要从相应的外部库更新符号定义,则返回 True</td>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.Linked.html" class="internal-link">Linked</a></td>
          <td>链接</td>
          <td>如果符号来自外部库,则返回 True</td>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.LinkLibraryPath.html" class="internal-link">LinkLibraryPath</a></td>
          <td>链接库路径</td>
          <td>返回插入符号的库的路径</td>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.Name.html" class="internal-link">Name</a></td>
          <td>名称</td>
          <td>指定符号定义的名称</td>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.Nested.html" class="internal-link">Nested</a></td>
          <td>嵌套</td>
          <td>如果符号定义包含子符号,则返回 True</td>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.NestedSymbols.html" class="internal-link">NestedSymbols</a></td>
          <td>嵌套符号</td>
          <td>返回所有子符号的集合</td>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.Type.html" class="internal-link">Type</a></td>
          <td>类型</td>
          <td>返回符号的类型</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1726" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,15">
<div class="content-fragment-content">


      <div class="content-fragment-header">方法:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.BreakLink.html" class="internal-link">BreakLink</a></td>
          <td>断链</td>
          <td>断开与外部库的链接并使定义本地化</td>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.Copy.html" class="internal-link">Copy</a></td>
          <td>复制</td>
          <td>将定义复制到剪贴板</td>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.Delete.html" class="internal-link">Delete</a></td>
          <td>删除</td>
          <td>从当前库中删除符号定义</td>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.Duplicate.html" class="internal-link">Duplicate</a></td>
          <td>复制</td>
          <td>复制符号定义</td>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.EnterEditMode.html" class="internal-link">EnterEditMode</a></td>
          <td>进入编辑模式</td>
          <td>开始编辑符号定义</td>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.FixLink.html" class="internal-link">FixLink</a></td>
          <td>修复链接</td>
          <td>恢复到外部库的断开链接</td>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.LeaveEditMode.html" class="internal-link">LeaveEditMode</a></td>
          <td>离开编辑模式</td>
          <td>完成编辑符号定义</td>
        </tr>
            <tr>
          <td><a href="./SymbolDefinition.UpdateLinks.html" class="internal-link">UpdateLinks</a></td>
          <td>更新链接</td>
          <td>从其外部库更新定义</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
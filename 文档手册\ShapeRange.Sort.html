<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>ShapeRange.Sort</title> 
                        </head><body>
                        <div><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><a href="./ShapeRange.html" class="internal-link"><strong>ShapeRange</strong></a></div>
<div class="content-fragment-header"><a href="./ShapeRange.Sort.html" class="internal-link"><strong>ShapeRange.Sort</strong></a></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">根据 CQL 条件表达式对范围内的形状进行排序</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <div class="content-fragment-header">语法:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
      <div class="code-block"><span class="keyword">Sub</span><span class="space"> </span> <span class="identifier">Sort</span><span class="punctuation">(</span><span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">CompareExpression</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">String</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">StartIndex</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Long</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="number">0</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">EndIndex</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Long</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="number">0</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">Data</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Variant</span><span class="punctuation">)</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1721" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,10">
<div class="content-fragment-content">


      <div class="content-fragment-header">参数:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名称</th>
          <th>类型</th>
          <th>描述</th>
        </tr>
            <tr>
          <td>CompareExpression</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">String</span></div>
                      </td>
          <td><div>指定查询表达式。有关创建查询表达式的帮助,请参阅“了解可用的查询条件”部分</div></td>
        </tr>
            <tr>
          <td>StartIndex</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Long</span></div>
                      </td>
          <td><div>指定形状范围内第一个形状对象的索引号。</div></td>
        </tr>
            <tr>
          <td>EndIndex</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Long</span></div>
                      </td>
          <td><div>指定形状范围内最后一个形状对象的索引号。</div></td>
        </tr>
            <tr>
          <td>Data</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Variant</span></div>
                      </td>
          <td><div>将 CQL 表达式可以引用的任何数据指定为 @data。例如,</div><div>相当于</div></td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>Sort</b> 方法根据 Corel 查询语言 (CQL) 条件表达式对范围内的形状进行排序。</div>
                <div>有关 CQL 的信息,请参阅“在宏中包含查询”部分</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div id="fragment-1725"></div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1730" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,19">
<div class="content-fragment-content">


      <div class="content-fragment-header">示例:</div>
            <div style="margin-bottom: 1em">以下 VBA 示例演示了如何使用 <b>ShapeRange.Sort</b> 方法:</div>
        <div><pre style="code">Sub ShapeRange_Sort()<br>
  <br>
 Dim d As Document<br>
 Dim sr As New ShapeRange 'Holds all the text shapes / numbers<br>
 Dim s As Shape, s1 As Shape, s2 As Shape<br>
 Dim y As Integer 'Holds the y position value of the rectangle<br>
 Dim x As Integer 'Holds the x position value of the rectangle<br>
 Dim i As Integer 'Holds the brick counter<br>
  <br>
 Set d = CreateDocument<br>
 'Create Bricks<br>
 For y = 1 To 9<br>
 For x = 1 To 6<br>
 i = i + 1<br>
 Set s1 = d.ActiveLayer.CreateRectangle2(x, y, 1, 0.5)<br>
 'Creates a Rectangle Shape<br>
 Set s2 = d.ActiveLayer.CreateArtisticText(0, 0, i)<br>
 'Creates a Number for the Brick Shape<br>
 s2.AlignToShape cdrAlignVCenter, s1<br>
 'Aligns the Text Vertically to the rectangle<br>
 s2.AlignToShape cdrAlignHCenter, s1<br>
 'Aligns the Text Horizontally to the rectangle<br>
 sr.Add s2<br>
 Next x<br>
 Next y<br>
 For y = 1 To 9<br>
 For x = 1 To 6<br>
 i = i + 1<br>
 Set s1 = d.ActiveLayer.CreateRectangle2(x, y + 0.5, 1, 0.5)<br>
 Set s2 = d.ActiveLayer.CreateArtisticText(0, 0, i)<br>
 s2.AlignToShape cdrAlignVCenter, s1<br>
 s2.AlignToShape cdrAlignHCenter, s1<br>
 sr.Add s2<br>
 Next x<br>
 Next y<br>
  <br>
 'Sort the numbers from the upper left to the lower right<br>
 '(going right and then down). To do this, give the highest value<br>
 'to the upper-left shape by multiplying its top position by 100<br>
 'and subtracting its left position.<br>
 'Example Shape 1 has a top value of 10 and a left value of 1 so ...<br>
 'Shape 1 (Upper Left Shape) -&gt; 10 X 100 - 1 = 999<br>
 'Shape 2 (to the right of Shape 1) -&gt; 10 x 100 - 2 = 998<br>
 'Bottom right Shape would be 1.5 x 100 - 6 = 144<br>
 MsgBox "Click OK to Sort the Bricks", vbOKOnly, "ShapeRange.Sort Example"<br>
 sr.Sort " @shape1.Top * 100 - @shape1.Left &gt; @shape2.Top * 100 - @shape2.Left"<br>
  <br>
 i = 0<br>
 For Each s In sr.Shapes<br>
 i = i + 1<br>
 s.Text.Story.Text = i<br>
 Next s<br>
  <br>
End Sub</pre></div>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
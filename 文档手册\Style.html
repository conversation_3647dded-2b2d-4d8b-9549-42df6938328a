<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>Style</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./Style.html" class="internal-link">Style</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>Style class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">风格类</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">Style</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>Style</b> 类表示文档中的对象样式和样式集。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./Style.BasedOn.html" class="internal-link">BasedOn</a></td>
          <td>基于</td>
          <td>返回此样式或样式集派生自的样式</td>
        </tr>
            <tr>
          <td><a href="./Style.CategoryName.html" class="internal-link">CategoryName</a></td>
          <td>分类名称</td>
          <td>返回样式或样式集的类别名称</td>
        </tr>
            <tr>
          <td><a href="./Style.Character.html" class="internal-link">Character</a></td>
          <td>特点</td>
          <td>返回字符样式属性</td>
        </tr>
            <tr>
          <td><a href="./Style.DerivedStyles.html" class="internal-link">DerivedStyles</a></td>
          <td>派生样式</td>
          <td>返回派生自此样式或样式集的样式或样式集的列表</td>
        </tr>
            <tr>
          <td><a href="./Style.DisplayCategoryName.html" class="internal-link">DisplayCategoryName</a></td>
          <td>显示类别名称</td>
          <td>返回样式或样式集的 UI 友好类别名称</td>
        </tr>
            <tr>
          <td><a href="./Style.DisplayName.html" class="internal-link">DisplayName</a></td>
          <td>显示名称</td>
          <td>返回样式集样式的 UI 友好名称</td>
        </tr>
            <tr>
          <td><a href="./Style.Fill.html" class="internal-link">Fill</a></td>
          <td>充满</td>
          <td>返回填充样式属性</td>
        </tr>
            <tr>
          <td><a href="./Style.Frame.html" class="internal-link">Frame</a></td>
          <td>框架</td>
          <td>返回框架样式属性</td>
        </tr>
            <tr>
          <td><a href="./Style.IsObjectDefaults.html" class="internal-link">IsObjectDefaults</a></td>
          <td>是对象默认值</td>
          <td>如果此样式集表示特定对象类型的默认样式属性,则返回 true</td>
        </tr>
            <tr>
          <td><a href="./Style.IsStyleSet.html" class="internal-link">IsStyleSet</a></td>
          <td>样式集</td>
          <td>如果这是一个样式集,则返回 true</td>
        </tr>
            <tr>
          <td><a href="./Style.Name.html" class="internal-link">Name</a></td>
          <td>名称</td>
          <td>返回样式或样式集的名称</td>
        </tr>
            <tr>
          <td><a href="./Style.Outline.html" class="internal-link">Outline</a></td>
          <td>轮廓</td>
          <td>返回轮廓样式属性</td>
        </tr>
            <tr>
          <td><a href="./Style.Paragraph.html" class="internal-link">Paragraph</a></td>
          <td>段落</td>
          <td>返回段落样式属性</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1726" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,15">
<div class="content-fragment-content">


      <div class="content-fragment-header">方法:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./Style.ClearProperty.html" class="internal-link">ClearProperty</a></td>
          <td>明确的财产</td>
          <td>删除属性的本地覆盖</td>
        </tr>
            <tr>
          <td><a href="./Style.Delete.html" class="internal-link">Delete</a></td>
          <td>删除</td>
          <td>删除样式</td>
        </tr>
            <tr>
          <td><a href="./Style.GetAllPropertyNames.html" class="internal-link">GetAllPropertyNames</a></td>
          <td>获取所有属性名称</td>
          <td>返回样式类别中所有属性名称的列表</td>
        </tr>
            <tr>
          <td><a href="./Style.GetOverridePropertyNames.html" class="internal-link">GetOverridePropertyNames</a></td>
          <td>获取覆盖属性名称</td>
          <td>返回样式或样式集中本地覆盖的属性的名称列表</td>
        </tr>
            <tr>
          <td><a href="./Style.GetProperty.html" class="internal-link">GetProperty</a></td>
          <td>获取属性</td>
          <td>按名称返回属性值</td>
        </tr>
            <tr>
          <td><a href="./Style.GetPropertyAsString.html" class="internal-link">GetPropertyAsString</a></td>
          <td>以字符串形式获取属性</td>
          <td>以字符串形式检索样式属性</td>
        </tr>
            <tr>
          <td><a href="./Style.IsPropertyInherited.html" class="internal-link">IsPropertyInherited</a></td>
          <td>是否继承财产</td>
          <td>如果属性是从父样式或样式集继承的,则返回 true</td>
        </tr>
            <tr>
          <td><a href="./Style.Rename.html" class="internal-link">Rename</a></td>
          <td>改名</td>
          <td>重命名样式</td>
        </tr>
            <tr>
          <td><a href="./Style.SetBasedOn.html" class="internal-link">SetBasedOn</a></td>
          <td>设置基于</td>
          <td>更改此样式派生自的父样式</td>
        </tr>
            <tr>
          <td><a href="./Style.SetProperty.html" class="internal-link">SetProperty</a></td>
          <td>设置属性</td>
          <td>按名称设置属性的值</td>
        </tr>
            <tr>
          <td><a href="./Style.SetPropertyAsString.html" class="internal-link">SetPropertyAsString</a></td>
          <td>将属性设置为字符串</td>
          <td>将属性值设置为字符串</td>
        </tr>
            <tr>
          <td><a href="./Style.StringAssign.html" class="internal-link">StringAssign</a></td>
          <td>字符串赋值</td>
          <td>从字符串恢复样式属性</td>
        </tr>
            <tr>
          <td><a href="./Style.ToString.html" class="internal-link">ToString</a></td>
          <td>字符串</td>
          <td>将所有样式属性转换为字符串</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
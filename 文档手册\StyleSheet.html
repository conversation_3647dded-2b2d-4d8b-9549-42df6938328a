<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>StyleSheet</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./StyleSheet.html" class="internal-link">StyleSheet</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>StyleSheet class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">样式表类</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">StyleSheet</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>StyleSheet </b> 类表示样式表中包含的样式和样式集。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./StyleSheet.AllStyles.html" class="internal-link">AllStyles</a></td>
          <td>所有样式</td>
          <td>返回样式表中所有样式的列表</td>
        </tr>
            <tr>
          <td><a href="./StyleSheet.AllStyleSets.html" class="internal-link">AllStyleSets</a></td>
          <td>所有样式集</td>
          <td>返回样式表中所有样式集的列表</td>
        </tr>
            <tr>
          <td><a href="./StyleSheet.ObjectDefaults.html" class="internal-link">ObjectDefaults</a></td>
          <td>对象默认值</td>
          <td>返回对象默认样式集</td>
        </tr>
            <tr>
          <td><a href="./StyleSheet.Styles.html" class="internal-link">Styles</a></td>
          <td>风格</td>
          <td>返回样式表中定义的顶级样式</td>
        </tr>
            <tr>
          <td><a href="./StyleSheet.StyleSets.html" class="internal-link">StyleSets</a></td>
          <td>样式集</td>
          <td>返回样式表中定义的顶级样式集</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1726" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,15">
<div class="content-fragment-content">


      <div class="content-fragment-header">方法:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./StyleSheet.CreateStyle.html" class="internal-link">CreateStyle</a></td>
          <td>创建样式</td>
          <td>创造新风格</td>
        </tr>
            <tr>
          <td><a href="./StyleSheet.CreateStyleFromShape.html" class="internal-link">CreateStyleFromShape</a></td>
          <td>创建形状的风格</td>
          <td>从形状创建样式</td>
        </tr>
            <tr>
          <td><a href="./StyleSheet.CreateStyleFromShapeRange.html" class="internal-link">CreateStyleFromShapeRange</a></td>
          <td>从形状范围内创建样式</td>
          <td>从形状范围创建样式</td>
        </tr>
            <tr>
          <td><a href="./StyleSheet.CreateStyleFromTextRange.html" class="internal-link">CreateStyleFromTextRange</a></td>
          <td>创建文本范围的样式</td>
          <td>从文本范围创建样式</td>
        </tr>
            <tr>
          <td><a href="./StyleSheet.CreateStyleSet.html" class="internal-link">CreateStyleSet</a></td>
          <td>创建样式集</td>
          <td>创建新样式集</td>
        </tr>
            <tr>
          <td><a href="./StyleSheet.CreateStyleSetFromShape.html" class="internal-link">CreateStyleSetFromShape</a></td>
          <td>创建从形状设置的样式</td>
          <td>从形状创建样式集</td>
        </tr>
            <tr>
          <td><a href="./StyleSheet.CreateStyleSetFromShapeRange.html" class="internal-link">CreateStyleSetFromShapeRange</a></td>
          <td>创建从形状范围设置的样式</td>
          <td>从形状范围创建样式集</td>
        </tr>
            <tr>
          <td><a href="./StyleSheet.CreateStyleSetFromTextRange.html" class="internal-link">CreateStyleSetFromTextRange</a></td>
          <td>创建从文本范围设置的样式</td>
          <td>从文本范围创建样式集</td>
        </tr>
            <tr>
          <td><a href="./StyleSheet.Export.html" class="internal-link">Export</a></td>
          <td>出口</td>
          <td>从样式表中导出样式</td>
        </tr>
            <tr>
          <td><a href="./StyleSheet.FindStyle.html" class="internal-link">FindStyle</a></td>
          <td>查找样式</td>
          <td>按名称查找样式</td>
        </tr>
            <tr>
          <td><a href="./StyleSheet.Import.html" class="internal-link">Import</a></td>
          <td>进口</td>
          <td>将样式导入样式表</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
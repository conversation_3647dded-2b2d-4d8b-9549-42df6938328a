<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>TableRow</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./TableRow.html" class="internal-link">TableRow</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>TableRow class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;"></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">TableRow</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>TableRow</b> 类表示表格行的设置。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./TableRow.Cells.html" class="internal-link">Cells</a></td>
          <td>细胞</td>
          <td>返回该行的单元格</td>
        </tr>
            <tr>
          <td><a href="./TableRow.Height.html" class="internal-link">Height</a></td>
          <td>高度</td>
          <td>指定行的高度</td>
        </tr>
            <tr>
          <td><a href="./TableRow.Index.html" class="internal-link">Index</a></td>
          <td>指数</td>
          <td>返回当前行的索引</td>
        </tr>
            <tr>
          <td><a href="./TableRow.IsFirst.html" class="internal-link">IsFirst</a></td>
          <td>是第一</td>
          <td>确定行是表的第一行</td>
        </tr>
            <tr>
          <td><a href="./TableRow.IsLast.html" class="internal-link">IsLast</a></td>
          <td>是最后一个</td>
          <td>确定行是表的最后一行</td>
        </tr>
            <tr>
          <td><a href="./TableRow.Next.html" class="internal-link">Next</a></td>
          <td>下一个</td>
          <td>返回表格的下一行</td>
        </tr>
            <tr>
          <td><a href="./TableRow.Previous.html" class="internal-link">Previous</a></td>
          <td>以前的</td>
          <td>返回表格的上一行</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1726" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,15">
<div class="content-fragment-content">


      <div class="content-fragment-header">方法:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./TableRow.AddToSelection.html" class="internal-link">AddToSelection</a></td>
          <td>添加到选择</td>
          <td>将行的所有单元格添加到当前选择</td>
        </tr>
            <tr>
          <td><a href="./TableRow.Delete.html" class="internal-link">Delete</a></td>
          <td>删除</td>
          <td>删除行</td>
        </tr>
            <tr>
          <td><a href="./TableRow.Select.html" class="internal-link">Select</a></td>
          <td>选择</td>
          <td>选择行的所有单元格</td>
        </tr>
            <tr>
          <td><a href="./TableRow.SetHeight.html" class="internal-link">SetHeight</a></td>
          <td>设置高度</td>
          <td>设置行高</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
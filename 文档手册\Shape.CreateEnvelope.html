<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>Shape.CreateEnvelope</title> 
                        </head><body>
                        <div><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><a href="./Shape.html" class="internal-link"><strong>Shape</strong></a></div>
<div class="content-fragment-header"><a href="./Shape.CreateEnvelope.html" class="internal-link"><strong>Shape.CreateEnvelope</strong></a></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">创建包络效果</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <div class="content-fragment-header">语法:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
      <div class="code-block"><span class="keyword">Function</span><span class="space"> </span> <span class="identifier">CreateEnvelope</span><span class="punctuation">(</span><span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">PresetIndex</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Long</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">Mode</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./cdrEnvelopeMode.html" class="internal-link">cdrEnvelopeMode</a></span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="namespace">cdrEnvelopePutty</span><span class="punctuation">,</span><span class="space"> </span> <span class="keyword">Optional</span><span class="space"> </span> <span class="keyword">ByVal</span><span class="space"> </span> <span class="parameter">KeepLines</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="keyword">Boolean</span><span class="space"> </span> <span class="punctuation">=</span><span class="space"> </span> <span class="keyword">False</span><span class="punctuation">)</span><span class="space"> </span> <span class="keyword">As</span><span class="space"> </span> <span class="namespace"><a href="./Effect.html" class="internal-link">Effect</a></span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1721" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,10">
<div class="content-fragment-content">


      <div class="content-fragment-header">参数:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名称</th>
          <th>类型</th>
          <th>描述</th>
        </tr>
            <tr>
          <td>PresetIndex</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Long</span></div>
                      </td>
          <td><div>指定唯一标识预设包络效果的索引号</div></td>
        </tr>
            <tr>
          <td>Mode</td>
          <td>
                                                                                                                                                                                                                <div class="code-block"><span class="namespace"><a href="./cdrEnvelopeMode.html" class="internal-link">cdrEnvelopeMode</a></span></div>
                      </td>
          <td><div>指定信封的映射模式类型,并返回 <a href="./cdrEnvelopeMode.html">cdrEnvelopeMode</a>。</div></td>
        </tr>
            <tr>
          <td>KeepLines</td>
          <td>
                                                                                                                                                                            <div class="code-block"><span class="keyword">Boolean</span></div>
                      </td>
          <td><div>指定封套是保持直线还是将其转换为曲线。如果 <b>KeepLines</b> 属性设置为 <b>True</b>，则信封保持直线。</div></td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>CreateEnvelope</b> 方法将形状插入到预设信封中,返回表示信封属性的 <a href="./Effect.html">Effect</a> 对象。</div>
                <div>目前只能应用预设的包络形状,节点位置不能修改。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div id="fragment-1725"></div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1730" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,19">
<div class="content-fragment-content">


      <div class="content-fragment-header">示例:</div>
            <div style="margin-bottom: 1em">下面的 VBA 示例绘制了一个放大镜。</div>
        <div><pre style="code">Sub Test()<br>
 Dim sText As Shape, sRect As Shape, sCircle1 As Shape, sCircle2 As Shape<br>
 Set sText = ActiveLayer.CreateArtisticText(2.8, 7.5, "Magnify")<br>
 With sText.Text.FontProperties<br>
 .Name = "Arial"<br>
 .Size = 64<br>
 End With<br>
 sText.Text.AlignProperties.Alignment = cdrCenterAlignment<br>
 sText.CreateEnvelope 4, cdrEnvelopePutty, False<br>
 ActiveDocument.ReferencePoint = cdrCenter<br>
 sText.Stretch 1, 1.6<br>
 Set sRect = ActiveLayer.CreateRectangle(2.164134, 5.358543, 6.338504, 4.798346)<br>
 With sRect<br>
 With .Fill.ApplyFountainFill(CreateRGBColor(255, 0, 0), CreateRGBColor(251, 0, 81), , -90)<br>
 .Colors.Add CreateRGBColor(255, 0, 0), 1<br>
 .Colors.Add CreateRGBColor(253, 253, 213), 23<br>
 .StartX = 4.251319<br>
 .StartY = 5.358543<br>
 .EndX = 4.251319<br>
 .EndY = 4.798346<br>
 End With<br>
 .Rotate -60<br>
 .Move 0.469843, -0.397559<br>
 .Outline.Type = cdrNoOutline<br>
 End With<br>
 Set sCircle1 = ActiveLayer.CreateEllipse(1.116024, 9.514843, 4.459134, 6.171732)<br>
 sCircle1.Outline.SetProperties 0.3, OutlineStyles(0), CreateCMYKColor(0, 100, 100, 0)<br>
 Set sCircle2 = sCircle1.Duplicate<br>
 With sCircle2.Outline<br>
 .Width = 0.05<br>
 .Color.CMYKAssign 0, 0, 0, 0<br>
 End With<br>
 sCircle2.CreateBlend sCircle1<br>
End Sub</pre></div>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
Sub SetNumbersPosition()
    Dim s As Shape
    Dim inputText As String
    Dim textContent As String
    Dim i As Long, j As Long
    Dim numStart As Long
    Dim numLength As Long
    Dim positionChoice As Integer
    
    ' 弹出输入框获取搜索内容
    inputText = InputBox("请输入你想要配合上下标的文本内容:", "设置数字标记位置")
    If inputText = "" Then Exit Sub
    
    ' 弹出选择框让用户选择字体位置
    positionChoice = CInt(InputBox("请选择字体位置:" & vbCrLf & _
                                "0 = 正常" & vbCrLf & _
                                "1 = 下标" & vbCrLf & _
                                "2 = 上标", "设置字体位置", "2"))
    
    ' 确保位置选择在有效范围内
    If positionChoice < 0 Or positionChoice > 2 Then
        positionChoice = 2 ' 默认为上标
    End If
    
    ' 创建命令组
    ActiveDocument.BeginCommandGroup "文本标记"
    
    ' 遍历所有选中的对象
    For Each s In ActiveSelectionRange
        ' 只处理文本对象
        If s.Type = cdrTextShape Then
            textContent = s.Text.Story.Text
            
            ' 查找输入文本在整个文本中的位置
            i = InStr(1, textContent, inputText)
            
            ' 处理所有匹配项
            While i > 0
                ' 查找输入文本后面的数字
                j = i + Len(inputText)
                
                If j <= Len(textContent) Then
                    numStart = j
                    numLength = 0
                    
                    ' 计算连续数字的长度
                    While j <= Len(textContent) And IsNumeric(Mid(textContent, j, 1))
                        numLength = numLength + 1
                        j = j + 1
                    Wend
                    
                    ' 如果找到数字，设置其位置
                    If numLength > 0 Then
                        Select Case positionChoice
                            Case 0 ' 正常
                                s.Text.FontPropertiesInRange(numStart, numLength).Position = cdrNormalFontPosition
                            Case 1 ' 下标
                                s.Text.FontPropertiesInRange(numStart, numLength).Position = cdrSubscriptFontPosition
                            Case 2 ' 上标
                                s.Text.FontPropertiesInRange(numStart, numLength).Position = cdrSuperscriptFontPosition
                        End Select
                    End If
                End If
                
                ' 查找下一个匹配项
                i = InStr(i + 1, textContent, inputText)
            Wend
        End If
    Next s
    
    ' 结束命令组
    ActiveDocument.EndCommandGroup
End Sub


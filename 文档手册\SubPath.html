<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>SubPath</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./SubPath.html" class="internal-link">SubPath</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>SubPath class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">表示曲线中子路径的设置</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">SubPath</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>Subpath </b> 类表示曲线中子路径的设置。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./SubPath.Application.html" class="internal-link">Application</a></td>
          <td>应用</td>
          <td>获取段集合所属的应用程序</td>
        </tr>
            <tr>
          <td><a href="./SubPath.Area.html" class="internal-link">Area</a></td>
          <td>区域</td>
          <td>返回闭合路径的面积</td>
        </tr>
            <tr>
          <td><a href="./SubPath.BoundingBox.html" class="internal-link">BoundingBox</a></td>
          <td>边界框</td>
          <td>返回子路径周围的边界矩形</td>
        </tr>
            <tr>
          <td><a href="./SubPath.Closed.html" class="internal-link">Closed</a></td>
          <td>关闭</td>
          <td>获取或设置子路径是打开还是关闭</td>
        </tr>
            <tr>
          <td><a href="./SubPath.EndNode.html" class="internal-link">EndNode</a></td>
          <td>端节点</td>
          <td>获取结束节点</td>
        </tr>
            <tr>
          <td><a href="./SubPath.FirstSegment.html" class="internal-link">FirstSegment</a></td>
          <td>第一段</td>
          <td>获取子路径的第一段</td>
        </tr>
            <tr>
          <td><a href="./SubPath.Index.html" class="internal-link">Index</a></td>
          <td>指数</td>
          <td>获取其曲线内子路径的索引</td>
        </tr>
            <tr>
          <td><a href="./SubPath.IsClockwise.html" class="internal-link">IsClockwise</a></td>
          <td>是顺时针</td>
          <td>如果路径方向主要是顺时针方向,则返回 True</td>
        </tr>
            <tr>
          <td><a href="./SubPath.LastSegment.html" class="internal-link">LastSegment</a></td>
          <td>最后一段</td>
          <td>获取子路径的最后一段</td>
        </tr>
            <tr>
          <td><a href="./SubPath.Length.html" class="internal-link">Length</a></td>
          <td>长度</td>
          <td>获取子路径的长度(以文档为单位)</td>
        </tr>
            <tr>
          <td><a href="./SubPath.Nodes.html" class="internal-link">Nodes</a></td>
          <td>节点</td>
          <td>获取子路径内的节点集合</td>
        </tr>
            <tr>
          <td><a href="./SubPath.Parent.html" class="internal-link">Parent</a></td>
          <td>家长</td>
          <td>获取子路径集合父</td>
        </tr>
            <tr>
          <td><a href="./SubPath.PositionX.html" class="internal-link">PositionX</a></td>
          <td>位置X</td>
          <td>获取或设置子路径头部的 X 位置</td>
        </tr>
            <tr>
          <td><a href="./SubPath.PositionY.html" class="internal-link">PositionY</a></td>
          <td>位置Y</td>
          <td>获取或设置子路径头部的 Y 位置</td>
        </tr>
            <tr>
          <td><a href="./SubPath.Segments.html" class="internal-link">Segments</a></td>
          <td>细分市场</td>
          <td>获取子路径中的段集合</td>
        </tr>
            <tr>
          <td><a href="./SubPath.SizeHeight.html" class="internal-link">SizeHeight</a></td>
          <td>尺寸高度</td>
          <td>获取整个子路径的高度(以文档为单位)</td>
        </tr>
            <tr>
          <td><a href="./SubPath.SizeWidth.html" class="internal-link">SizeWidth</a></td>
          <td>尺寸宽度</td>
          <td>获取整个子路径的宽度(以文档为单位)</td>
        </tr>
            <tr>
          <td><a href="./SubPath.StartNode.html" class="internal-link">StartNode</a></td>
          <td>起始节点</td>
          <td>获取起始节点</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1726" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,15">
<div class="content-fragment-content">


      <div class="content-fragment-header">方法:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./SubPath.AddNodeAt.html" class="internal-link">AddNodeAt</a></td>
          <td>添加节点在</td>
          <td>在段上插入一个节点</td>
        </tr>
            <tr>
          <td><a href="./SubPath.AppendCurveSegment.html" class="internal-link">AppendCurveSegment</a></td>
          <td>追加曲线段</td>
          <td>将新曲线段附加到此子路径</td>
        </tr>
            <tr>
          <td><a href="./SubPath.AppendCurveSegment2.html" class="internal-link">AppendCurveSegment2</a></td>
          <td>追加曲线段2</td>
          <td>将新曲线段附加到此子路径</td>
        </tr>
            <tr>
          <td><a href="./SubPath.AppendLineSegment.html" class="internal-link">AppendLineSegment</a></td>
          <td>追加线段</td>
          <td>将新线段附加到此子路径</td>
        </tr>
            <tr>
          <td><a href="./SubPath.BreakApartAt.html" class="internal-link">BreakApartAt</a></td>
          <td>分手</td>
          <td>将此段分成两个未连接的段</td>
        </tr>
            <tr>
          <td><a href="./SubPath.Delete.html" class="internal-link">Delete</a></td>
          <td>删除</td>
          <td>擦除整个子路径</td>
        </tr>
            <tr>
          <td><a href="./SubPath.Extract.html" class="internal-link">Extract</a></td>
          <td>提炼</td>
          <td>将子路径提取到新的单独曲线中</td>
        </tr>
            <tr>
          <td><a href="./SubPath.FindClosestSegment.html" class="internal-link">FindClosestSegment</a></td>
          <td>查找最近段</td>
          <td>查找最接近指定点的线段</td>
        </tr>
            <tr>
          <td><a href="./SubPath.FindNodeAtPoint.html" class="internal-link">FindNodeAtPoint</a></td>
          <td>在点找到节点</td>
          <td>在指定点沿路径查找节点</td>
        </tr>
            <tr>
          <td><a href="./SubPath.FindSegmentAtPoint.html" class="internal-link">FindSegmentAtPoint</a></td>
          <td>在点找到段</td>
          <td>在指定点沿路径查找线段</td>
        </tr>
            <tr>
          <td><a href="./SubPath.FindSegmentOffset.html" class="internal-link">FindSegmentOffset</a></td>
          <td>找到段偏移</td>
          <td>查找段偏移</td>
        </tr>
            <tr>
          <td><a href="./SubPath.GetBoundingBox.html" class="internal-link">GetBoundingBox</a></td>
          <td>获取边界框</td>
          <td>获取围绕子路径的边界矩形</td>
        </tr>
            <tr>
          <td><a href="./SubPath.GetCopy.html" class="internal-link">GetCopy</a></td>
          <td>获取复制</td>
          <td>将当前子路径的副本作为单独的曲线返回</td>
        </tr>
            <tr>
          <td><a href="./SubPath.GetCurvatureAt.html" class="internal-link">GetCurvatureAt</a></td>
          <td>获取曲率在</td>
          <td>获取当前子路径处的曲线曲率</td>
        </tr>
            <tr>
          <td><a href="./SubPath.GetCurveInfo.html" class="internal-link">GetCurveInfo</a></td>
          <td>获取曲线信息</td>
          <td>检索低级曲线信息</td>
        </tr>
            <tr>
          <td><a href="./SubPath.GetCurveSpeedAt.html" class="internal-link">GetCurveSpeedAt</a></td>
          <td>获得曲线速度</td>
          <td>获取当前子路径处的曲线速度</td>
        </tr>
            <tr>
          <td><a href="./SubPath.GetIntersections.html" class="internal-link">GetIntersections</a></td>
          <td>获取交叉点</td>
          <td>找到与另一个子路径的交点</td>
        </tr>
            <tr>
          <td><a href="./SubPath.GetPerpendicularAt.html" class="internal-link">GetPerpendicularAt</a></td>
          <td>垂直于</td>
          <td>找到线段上一点的垂线</td>
        </tr>
            <tr>
          <td><a href="./SubPath.GetPointPositionAt.html" class="internal-link">GetPointPositionAt</a></td>
          <td>获取点位置</td>
          <td>获取位于子路径上的点的坐标</td>
        </tr>
            <tr>
          <td><a href="./SubPath.GetPolyline.html" class="internal-link">GetPolyline</a></td>
          <td>获取折线</td>
          <td>返回由近似当前子路径的线段组成的曲线</td>
        </tr>
            <tr>
          <td><a href="./SubPath.GetPosition.html" class="internal-link">GetPosition</a></td>
          <td>获取位置</td>
          <td>获取页面中子路径头部的水平和垂直位置</td>
        </tr>
            <tr>
          <td><a href="./SubPath.GetSegmentAt.html" class="internal-link">GetSegmentAt</a></td>
          <td>获取细分</td>
          <td>获取给定位置的段</td>
        </tr>
            <tr>
          <td><a href="./SubPath.GetTangentAt.html" class="internal-link">GetTangentAt</a></td>
          <td>相切于</td>
          <td>找到线段上一点的切线</td>
        </tr>
            <tr>
          <td><a href="./SubPath.IsOnSubPath.html" class="internal-link">IsOnSubPath</a></td>
          <td>在子路径上</td>
          <td>查找给定坐标是“内部”、“外部”还是在子路径的边缘</td>
        </tr>
            <tr>
          <td><a href="./SubPath.IsPointInside.html" class="internal-link">IsPointInside</a></td>
          <td>是点在里面</td>
          <td>确定指定点是否位于闭合路径内</td>
        </tr>
            <tr>
          <td><a href="./SubPath.IsRectOnEdge.html" class="internal-link">IsRectOnEdge</a></td>
          <td>是矩形在边缘</td>
          <td>确定指定的矩形是否与路径相交</td>
        </tr>
            <tr>
          <td><a href="./SubPath.Move.html" class="internal-link">Move</a></td>
          <td>移动</td>
          <td>将整个子路径移动给定的增量</td>
        </tr>
            <tr>
          <td><a href="./SubPath.Next.html" class="internal-link">Next</a></td>
          <td>下一个</td>
          <td>获取此子路径之后的子路径</td>
        </tr>
            <tr>
          <td><a href="./SubPath.Previous.html" class="internal-link">Previous</a></td>
          <td>以前的</td>
          <td>获取此子路径之前的子路径</td>
        </tr>
            <tr>
          <td><a href="./SubPath.PutCurveInfo.html" class="internal-link">PutCurveInfo</a></td>
          <td>放置曲线信息</td>
          <td>用提供的节点信息替换当前子路径</td>
        </tr>
            <tr>
          <td><a href="./SubPath.ReverseDirection.html" class="internal-link">ReverseDirection</a></td>
          <td>反向</td>
          <td>反转子路径中的节点顺序</td>
        </tr>
            <tr>
          <td><a href="./SubPath.Selection.html" class="internal-link">Selection</a></td>
          <td>选择</td>
          <td>创建一个包含此子路径上所有选择节点的范围</td>
        </tr>
            <tr>
          <td><a href="./SubPath.SetPosition.html" class="internal-link">SetPosition</a></td>
          <td>设置位置</td>
          <td>设置子路径头部在页面上的水平和垂直位置</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>StructExportOptions</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./StructExportOptions.html" class="internal-link">StructExportOptions</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>StructExportOptions class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;">StructExportOptions 类</div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">StructExportOptions</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>StructExportOptions</b> 类表示导出设置。此类与 <a href="./Document.ExportEx.html">Document.ExportEx</a> 结合使用方法。</div>
                <div>您可以在 Visual Basic 中使用 <b>New</b> 关键字来创建 <b>StructExportOptions</b> 对象。</div>
                <div>此对象中指定的大多数设置都与位图相关。您可以使用 <a href="./Document.ExportBitmap.html">Document.ExportBitmap</a> 方法来指定这些参数作为函数调用的直接参数。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./StructExportOptions.AlwaysOverprintBlack.html" class="internal-link">AlwaysOverprintBlack</a></td>
          <td>总是套印黑色</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructExportOptions.AntiAliasingType.html" class="internal-link">AntiAliasingType</a></td>
          <td>抗锯齿类型</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructExportOptions.Compression.html" class="internal-link">Compression</a></td>
          <td>压缩</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructExportOptions.Dithered.html" class="internal-link">Dithered</a></td>
          <td>抖动的</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructExportOptions.ExportArea.html" class="internal-link">ExportArea</a></td>
          <td>出口区</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructExportOptions.ImageType.html" class="internal-link">ImageType</a></td>
          <td>图像类型</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructExportOptions.MaintainAspect.html" class="internal-link">MaintainAspect</a></td>
          <td>维护方面</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructExportOptions.MaintainLayers.html" class="internal-link">MaintainLayers</a></td>
          <td>维护层</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructExportOptions.MatteColor.html" class="internal-link">MatteColor</a></td>
          <td>哑光色</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructExportOptions.Matted.html" class="internal-link">Matted</a></td>
          <td>哑光</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructExportOptions.MatteMaskedOnly.html" class="internal-link">MatteMaskedOnly</a></td>
          <td>哑光MaskedOnly</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructExportOptions.Overwrite.html" class="internal-link">Overwrite</a></td>
          <td>覆盖</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructExportOptions.ProofColorSettings.html" class="internal-link">ProofColorSettings</a></td>
          <td>证明颜色设置</td>
          <td>指定要用于导出的颜色校样设置</td>
        </tr>
            <tr>
          <td><a href="./StructExportOptions.ResolutionX.html" class="internal-link">ResolutionX</a></td>
          <td>分辨率X</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructExportOptions.ResolutionY.html" class="internal-link">ResolutionY</a></td>
          <td>分辨率Y</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructExportOptions.SizeX.html" class="internal-link">SizeX</a></td>
          <td>尺寸X</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructExportOptions.SizeY.html" class="internal-link">SizeY</a></td>
          <td>尺寸Y</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructExportOptions.Transparent.html" class="internal-link">Transparent</a></td>
          <td>透明的</td>
          <td></td>
        </tr>
            <tr>
          <td><a href="./StructExportOptions.UseColorProfile.html" class="internal-link">UseColorProfile</a></td>
          <td>使用颜色配置文件</td>
          <td></td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1726"></div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>
<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=gbk">
<meta charset='utf-8' http-equiv="Content-Language" content="zh-CN"> 
                        <title>TableShape</title> 
                        </head><body>
                        <div><h3>标题</h3><a href="./TableShape.html" class="internal-link">TableShape</a></div><div><h3>详情标题</h3><p><div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header top-border responsive-1" id="fragment-1718" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,7">
<div class="content-fragment-content">



  
          
    
  <div class="content-fragment-header"><strong>TableShape class</strong></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1719" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,8">
<div class="content-fragment-content">


  <div style="margin-top:20px;"></div>
    </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment sdk-syntax no-wrapper with-spacing with-header responsive-1" id="fragment-1720" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,9">
<div class="content-fragment-content">



                                                                                                                                                            <div class="content-fragment-header">语法:</div>
      <div class="code-block"><span class="keyword">Class</span><span class="space"> </span> <span class="identifier">TableShape</span></div>
        </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1721"></div>
<div id="fragment-1722"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1723" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,12">
<div class="content-fragment-content">


  <div class="content-fragment-header">备注:</div>
              <div><b>TableShape</b> 类表示形状(<a href="./Shape.html">Shape</a>对象)的特定于表格的设置。</div>
                <div>表格是通过 <a href="./Layer.CreateCustomShape.html">Layer.CreateCustomShape</a> 方法创建的。 <b>TableShape</b> 类和以下与表格相关的类可供您使用，尽管它们并未直接集成到 CorelDRAW 对象模型中：</div>
                <div><a href="./TableBorders.html">TableBorders</a></div>
                <div><a href="./TableCell.html">TableCell</a></div>
                <div><a href="./TableCellRange.html">TableCellRange</a></div>
                <div><a href="./TableCells.html">TableCells</a></div>
                <div><a href="./TableColumn.html">TableColumn</a></div>
                <div><a href="./TableColumns.html">TableColumns</a></div>
                <div><a href="./TableRow.html">TableRow</a></div>
                <div><a href="./TableRows.html">TableRows</a></div>
                <div><b>TableShape</b> 类的属性和方法通过 <a href="https://community.coreldraw.com//sdk/api/draw/17/p/Shape.Custom?lang =vba">Shape.Custom</a> 属性。</div>
                <div>有关相关代码示例,请参阅 <a href="./Layer.CreateCustomShape.html">Layer.CreateCustomShape</a>。</div>
          </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1724"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1725" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,14">
<div class="content-fragment-content">


      <div class="content-fragment-header">属性:</div><!--cdr&#x5F00;&#x53D1;&#x6587;&#x6863;X7&#xFF08;&#x6C49;&#x82F1;&#x53CC;&#x8BED;&#x5BF9;&#x7167;&#x7248;&#xFF09;-luzc&#x6C49;&#x5316;-&#x4EC5;&#x4F9B;&#x5B66;&#x4E60;&#x4EA4;&#x6D41;-->
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./TableShape.Borders.html" class="internal-link">Borders</a></td>
          <td>边框</td>
          <td>返回表格边框设置</td>
        </tr>
            <tr>
          <td><a href="./TableShape.Cells.html" class="internal-link">Cells</a></td>
          <td>细胞</td>
          <td>返回表格单元格的集合</td>
        </tr>
            <tr>
          <td><a href="./TableShape.CellSpacingX.html" class="internal-link">CellSpacingX</a></td>
          <td>单元格间距X</td>
          <td>指定水平单元格间距</td>
        </tr>
            <tr>
          <td><a href="./TableShape.CellSpacingY.html" class="internal-link">CellSpacingY</a></td>
          <td>单元格间距Y</td>
          <td>指定垂直单元格间距</td>
        </tr>
            <tr>
          <td><a href="./TableShape.Columns.html" class="internal-link">Columns</a></td>
          <td>列</td>
          <td>返回表的列的集合</td>
        </tr>
            <tr>
          <td><a href="./TableShape.Rows.html" class="internal-link">Rows</a></td>
          <td>行</td>
          <td>返回表的行集合</td>
        </tr>
            <tr>
          <td><a href="./TableShape.Selection.html" class="internal-link">Selection</a></td>
          <td>选择</td>
          <td>返回选定的单元格</td>
        </tr>
            <tr>
          <td><a href="./TableShape.SeparatedBorders.html" class="internal-link">SeparatedBorders</a></td>
          <td>分离订单</td>
          <td>指定单元格边框是分开还是折叠</td>
        </tr>
            <tr>
          <td><a href="./TableShape.TypeID.html" class="internal-link">TypeID</a></td>
          <td>类型ID</td>
          <td></td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1726" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,15">
<div class="content-fragment-content">


      <div class="content-fragment-header">方法:</div>
    <table border="1" cellpadding="5" cellspacing="0">
      <tbody>
        <tr>
          <th>名字</th><th>译文</th><th>描述</th>
        </tr>
            <tr>
          <td><a href="./TableShape.AddColumn.html" class="internal-link">AddColumn</a></td>
          <td>添加列</td>
          <td>添加表格列</td>
        </tr>
            <tr>
          <td><a href="./TableShape.AddRow.html" class="internal-link">AddRow</a></td>
          <td>添加行</td>
          <td>添加表格行</td>
        </tr>
            <tr>
          <td><a href="./TableShape.Cell.html" class="internal-link">Cell</a></td>
          <td>细胞</td>
          <td>返回表格的指定单元格</td>
        </tr>
            <tr>
          <td><a href="./TableShape.CellRange.html" class="internal-link">CellRange</a></td>
          <td>细胞范围</td>
          <td>返回表格的一系列单元格</td>
        </tr>
            <tr>
          <td><a href="./TableShape.ClearSelection.html" class="internal-link">ClearSelection</a></td>
          <td>清空选项</td>
          <td>取消选择表格中的所有单元格</td>
        </tr>
            <tr>
          <td><a href="./TableShape.ConvertToText.html" class="internal-link">ConvertToText</a></td>
          <td>转换为文本</td>
          <td>将表格转换为文本</td>
        </tr>
            <tr>
          <td><a href="./TableShape.CreateEmptyCellRange.html" class="internal-link">CreateEmptyCellRange</a></td>
          <td>创建EmptyCellRange</td>
          <td>创建一个空的单元格范围对象</td>
        </tr>
            <tr>
          <td><a href="./TableShape.Delete.html" class="internal-link">Delete</a></td>
          <td>删除</td>
          <td>删除表</td>
        </tr>
            <tr>
          <td><a href="./TableShape.Select.html" class="internal-link">Select</a></td>
          <td>选择</td>
          <td>选择表格中的所有单元格</td>
        </tr>
            <tr>
          <td><a href="./TableShape.SetCellSpacing.html" class="internal-link">SetCellSpacing</a></td>
          <td>设置单元格间距</td>
          <td>设置水平和垂直单元格间距</td>
        </tr>
        </tbody>
    </table>
      </div>
<div class="content-fragment-footer"></div>
</div>
<div id="fragment-1727"></div>
<div id="fragment-1728"></div>
<div id="fragment-1729"></div>
<div id="fragment-1730"></div>
<div class="content-fragment scripted-content-fragment no-wrapper with-spacing with-header responsive-1" id="fragment-1731" data-reflow="ctl02_ctl02_content,ctl02_ctl02_singlecolumn,1,1,20">
<div class="content-fragment-content">


      
    
      </div>
<div class="content-fragment-footer"></div>
</div></p></div>
                        </body></html>